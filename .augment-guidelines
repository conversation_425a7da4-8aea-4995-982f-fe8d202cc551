# .augment-guidelines – Provider‑based Flutter project
# ---------------------------------------------------

# ========= General =========
- **Language:** Dart>=3.4, fully null‑safe.
- **State management:** provider^6; **always prefer `context.read/watch/select`**.

# ========= Provider usage =========
- **READ (one‑off):** use `context.read<Foo>()`; **avoid** `Provider.of<Foo>(context, listen: false)`.
- **LISTEN (reactive):** use `context.watch<Foo>()` to rebuild the whole widget, or `context.select<Foo, T>((p) => p.someField)` to minimise rebuilds.
- **CREATE:** declare `ChangeNotifierProvider` / `Provider` inside a dedicated `providers.dart` per feature, **not** in `main.dart`.
- **MUTATE:** state‑update logic lives only inside `FooNotifier` / `FooController`; the UI must never call `notifyListeners()` directly.

# ========= Naming& Structure =========
- **Files:** `snake_case.dart`; one primary class per file.
- **Classes:** `PascalCase`; notifiers end with **Notifier** (e.g. `AuthNotifier`).


# ========= UI conventions =========
- Prefer `StatelessWidget` whenever possible; use `StatefulWidget` **only** when `initState`/`dispose` or local animations are required.
- **Styling:** centralised via `Theme.of(context)` or a custom `AppTheme`; never hard‑code colours.

# ========= Anti‑patterns =========
- 🚫 Accessing Provider through `Provider.of` except where no `BuildContext` is available.
- 🚫 Calling APIs directly inside a Widget.

# ========= Response format =========
- When Augment answers: provide (1) an explanation ≤4 sentences, then (2) a complete code snippet.

