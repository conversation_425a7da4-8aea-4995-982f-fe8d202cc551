{"java.configuration.updateBuildConfiguration": "interactive", "[dart]": {"editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [80], "editor.selectionHighlight": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggestSelection": "first", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "off"}, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit", "quickfix.add.required": "explicit", "quickfix.add.required.multi": "explicit", "quickfix.create.constructorForFinalFields": "explicit"}, "files.associations": {"*.arb": "json"}, "explorer.fileNesting.enabled": true, "editor.stickyScroll.enabled": true, "explorer.fileNesting.expand": false, "explorer.fileNesting.patterns": {"pubspec.yaml": ".flutter-plugins, .packages, .dart_tool, .flutter-plugins-dependencies, .metadata, .packages, pubspec.lock", "analysis_options.yaml": " all_lint_rules.yaml", ".gitignore": ".gita<PERSON><PERSON><PERSON><PERSON>, .git<PERSON><PERSON><PERSON>, .git<PERSON><PERSON><PERSON>, .mailmap, .git-blame*", "readme.*": "authors, backers.md, changelog*, citation*, code_of_conduct.md, codeowners, contributing.md, contributors, copying, credits, governance.md, history.md, license*, maintainers, readme*, security.md, sponsors.md", "*.dart": "$(capture).g.dart, $(capture).freezed.dart, $(capture).gr.dart", "appdist.*": "clean*, e2e*, commit-app-dist*, firebase*, format*, prod*", "mason.yaml": "mason-lock.json"}, "dart.flutterSdkPath": ".fvm/versions/stable"}