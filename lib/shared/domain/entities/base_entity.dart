import 'package:equatable/equatable.dart';

/// Base entity class for all domain entities
abstract class BaseEntity extends Equatable {
  const BaseEntity({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Unique identifier
  final String id;
  
  /// Creation timestamp
  final DateTime createdAt;
  
  /// Last update timestamp
  final DateTime updatedAt;

  @override
  List<Object?> get props => [id, createdAt, updatedAt];
  
  /// Check if entity is new (not persisted)
  bool get isNew => id.isEmpty;
  
  /// Check if entity has been modified recently
  bool get isRecentlyModified {
    final now = DateTime.now();
    final difference = now.difference(updatedAt);
    return difference.inMinutes < 5; // Modified within last 5 minutes
  }
  
  /// Get age of entity
  Duration get age => DateTime.now().difference(createdAt);
  
  /// Check if entity was created today
  bool get isCreatedToday {
    final now = DateTime.now();
    return createdAt.year == now.year &&
           createdAt.month == now.month &&
           createdAt.day == now.day;
  }
  
  /// Check if entity was updated today
  bool get isUpdatedToday {
    final now = DateTime.now();
    return updatedAt.year == now.year &&
           updatedAt.month == now.month &&
           updatedAt.day == now.day;
  }
}
