import 'package:dartz/dartz.dart';

import '../../../core/errors/failures.dart';

/// Base use case interface
abstract class UseCase<Type, Params> {
  /// Execute the use case
  Future<Either<Failure, Type>> call(Params params);
}

/// Use case with no parameters
abstract class NoParamsUseCase<Type> {
  /// Execute the use case
  Future<Either<Failure, Type>> call();
}

/// Use case that returns a stream
abstract class StreamUseCase<Type, Params> {
  /// Execute the use case and return a stream
  Stream<Either<Failure, Type>> call(Params params);
}

/// Use case with no parameters that returns a stream
abstract class NoParamsStreamUseCase<Type> {
  /// Execute the use case and return a stream
  Stream<Either<Failure, Type>> call();
}

/// No parameters class for use cases that don't need parameters
class NoParams {
  const NoParams();
}

/// Base parameters class for use cases
abstract class UseCaseParams {
  const UseCaseParams();
}

/// Pagination parameters
class PaginationParams extends UseCaseParams {
  const PaginationParams({
    this.page = 1,
    this.limit = 20,
    this.offset,
  });

  final int page;
  final int limit;
  final int? offset;

  int get calculatedOffset => offset ?? (page - 1) * limit;

  @override
  String toString() => 'PaginationParams(page: $page, limit: $limit, offset: $offset)';
}

/// Search parameters
class SearchParams extends PaginationParams {
  const SearchParams({
    required this.query,
    super.page,
    super.limit,
    super.offset,
    this.filters,
    this.sortBy,
    this.sortOrder = SortOrder.asc,
  });

  final String query;
  final Map<String, dynamic>? filters;
  final String? sortBy;
  final SortOrder sortOrder;

  @override
  String toString() => 'SearchParams(query: $query, page: $page, limit: $limit, '
      'filters: $filters, sortBy: $sortBy, sortOrder: $sortOrder)';
}

/// Sort order enumeration
enum SortOrder {
  asc,
  desc,
}

/// Filter parameters
class FilterParams extends PaginationParams {
  const FilterParams({
    required this.filters,
    super.page,
    super.limit,
    super.offset,
    this.sortBy,
    this.sortOrder = SortOrder.asc,
  });

  final Map<String, dynamic> filters;
  final String? sortBy;
  final SortOrder sortOrder;

  @override
  String toString() => 'FilterParams(filters: $filters, page: $page, limit: $limit, '
      'sortBy: $sortBy, sortOrder: $sortOrder)';
}

/// ID parameters for operations that require an ID
class IdParams extends UseCaseParams {
  const IdParams({required this.id});

  final String id;

  @override
  String toString() => 'IdParams(id: $id)';
}

/// Create parameters for create operations
class CreateParams<T> extends UseCaseParams {
  const CreateParams({required this.data});

  final T data;

  @override
  String toString() => 'CreateParams(data: $data)';
}

/// Update parameters for update operations
class UpdateParams<T> extends UseCaseParams {
  const UpdateParams({
    required this.id,
    required this.data,
  });

  final String id;
  final T data;

  @override
  String toString() => 'UpdateParams(id: $id, data: $data)';
}

/// Delete parameters for delete operations
class DeleteParams extends UseCaseParams {
  const DeleteParams({required this.id});

  final String id;

  @override
  String toString() => 'DeleteParams(id: $id)';
}
