import 'package:flutter/material.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:simple_gradient_text/simple_gradient_text.dart';

import '../animation/scale_fade_transition.dart';
import '../shared/daily_inspiration/daily_inspiration_model.dart';

class ChooseCardScreen extends StatefulWidget {
  static const String routeName = '/card-screen';

  const ChooseCardScreen({super.key});

  @override
  State<ChooseCardScreen> createState() => _ChooseCardScreenState();
}

class _ChooseCardScreenState extends State<ChooseCardScreen>
    with SingleTickerProviderStateMixin {
  final duration = const Duration(seconds: 2);
  final maxItem = 4;

  late final AnimationController _animationController;

  final List<Animation<double>> _cardAnimations = [];
  final List<CurvedAnimation> _curvedAnimations = [];
  final List<Interval> _intervals = [];

  late final Animation<Offset> _slideTextAnimation;
  late final Animation<double> _textOpacityAnimation;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      context.read<DailyInspirationModel>().preloadImage(context);
    });

    _animationController = AnimationController(
      vsync: this,
      duration: duration,
    );

    _intervals.addAll([
      const Interval(0.0, 0.25, curve: Curves.easeOut),
      const Interval(0.15, 0.40, curve: Curves.easeOut),
      const Interval(0.3, 0.55, curve: Curves.easeOut),
      const Interval(0.45, 0.70, curve: Curves.easeOut),
    ]);

    _curvedAnimations.addAll(List.generate(maxItem, (index) {
      return CurvedAnimation(
        parent: _animationController,
        curve: _intervals[index],
      );
    }));

    _cardAnimations.addAll(List.generate(maxItem, (index) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(_curvedAnimations[index]);
    }));

    _slideTextAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: const Offset(0, 0),
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.7, 1.0, curve: Curves.easeInOutBack),
      ),
    );

    _textOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.8, 1.0, curve: Curves.easeOut),
      ),
    );

    Future.delayed(const Duration(milliseconds: 500), () {
      _animationController.forward();
    });
  }

  void _onTapCard(int index) {
    if (_animationController.isAnimating) {
      return;
    }

    context.startCardDetail(heroTag: index.toString());
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.kDarkBG,
      appBar: AppBarCustom(),
      body: SafeArea(
        child: Container(
          child: Stack(
            children: [
              Column(
                children: [
                  GradientText(
                    'Thông điệp \ngửi tới bạn hôm nay',
                    textAlign: TextAlign.center,
                    colors: [
                      const Color(0xffFF50D9),
                      const Color(0xffFFB077),
                      const Color(0xffFFD028),
                    ],
                    style: const TextStyle(
                      fontSize: 22,
                      height: 28 / 22,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  GridView.builder(
                    padding: const EdgeInsets.all(20),
                    shrinkWrap: true,
                    itemCount: maxItem,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      mainAxisSpacing: 12,
                      crossAxisSpacing: 12,
                      childAspectRatio: 0.73,
                    ),
                    itemBuilder: (context, index) {
                      return GestureDetector(
                        onTap: () => _onTapCard(index),
                        child: Hero(
                          tag: index.toString(),
                          child: ScaleFadeTransition(
                            scale: _cardAnimations[index],
                            opacity: _cardAnimations[index],
                            child: const _Card(),
                          ),
                        ),
                      );
                    },
                  ),
                  Expanded(
                    child: Center(
                      child: SlideTransition(
                        position: _slideTextAnimation,
                        child: FadeTransition(
                          opacity: _textOpacityAnimation,
                          child: const Text(
                            'Rút bài nhận thông điệp\n ngày hôm nay',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              decoration: TextDecoration.none,
                              fontSize: 17.0,
                              height: 22 / 17,
                              fontWeight: FontWeight.w400,
                              color: AppColors.subTitleColor,
                              fontFamily: 'MyFont3',
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 35),
                ],
              ),
              const Stack(
                children: [
                  Positioned(
                    bottom: 0,
                    left: 0,
                    child: ImageWidget(IconConstants.moon02),
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: ImageWidget(IconConstants.lotus03),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _Card extends StatelessWidget {
  const _Card();

  @override
  Widget build(BuildContext context) {
    return const ImageWidget(
      ImageConstants.card,
      fit: BoxFit.contain,
    );
  }
}
