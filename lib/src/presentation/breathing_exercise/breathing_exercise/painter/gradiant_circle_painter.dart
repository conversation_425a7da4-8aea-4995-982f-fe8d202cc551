import 'dart:math';

import 'package:flutter/material.dart';

class GradientCirclePainter extends CustomPainter {
  final double percent;

  GradientCirclePainter({
    required this.percent,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final heightImg = size.height;
    final widthImg = size.width;

    final double radius = widthImg / 2;
    final Offset center = Offset(widthImg / 2, heightImg / 2);
    final Rect rect = Rect.fromCircle(center: center, radius: radius);

    final Gradient gradient = const LinearGradient(
      colors: <Color>[
        Color(0xffFF842A),
        Color(0xffF8C614),
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );

    final Paint borderPaint = Paint()
      ..shader = gradient.createShader(rect)
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeWidth = 16.0;

    final value = percent == 0 ? 0 : (percent - 0.1);
    final valueRatio = value / 100;
    final posCircle = 0.5 - valueRatio;

    final Path path = Path()
      ..moveTo(center.dx, heightImg)
      ..arcTo(
        rect,
        posCircle * pi,
        2 * pi * valueRatio,
        true,
      );

    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is GradientCirclePainter) {
      return oldDelegate.percent != percent;
    }
    return false;
  }
}
