import 'package:flutter/material.dart';

import '../../../../core/widgets/common/image_widget.dart';

class CountBreathingWidget extends StatelessWidget {
  const CountBreathingWidget({
    super.key,
    required this.count,
    required this.icon,
    required this.label,
  });

  final String count;
  final String icon;
  final String label;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          width: 50,
          height: 32,
          child: ImageWidget(
            icon,
            fit: BoxFit.contain,
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              count,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 22,
                height: 28 / 22,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              label,
              style: const TextStyle(
                color: Color(0xff98A2B3),
                fontSize: 14,
                height: 19 / 14,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
