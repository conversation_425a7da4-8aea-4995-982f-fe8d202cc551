import 'package:meditaguru/src/core/base/bloc/base_bloc.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/entities/bottom_bar_item.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/app_bloc.dart';
import 'package:rxdart/rxdart.dart';

import 'navigation_model.dart';

class NavigationBloc extends BaseBloc {
  late PublishSubject<NavigationSate> navigationController;
  late NavigationSate navigationSate;

  @override
  Future init() async {
    navigationController = PublishSubject<NavigationSate>();
    var moduleSelectedDefault = BottomBarItemType.home;
    injector<AppBloc>().appModel.appConfig.tabBar?.forEach((element) {
      if (element.defaultSelected == true) {
        moduleSelectedDefault = element.layout;
        return;
      }
    });
    navigationSate = NavigationSate(
        moduleIdSelected: moduleSelectedDefault,
        navigationItems: injector<AppBloc>().appModel.appConfig.tabBar ?? []);
    navigationController.add(navigationSate);
  }

  @override
  Future dispose() async {
    await navigationController.close();
  }

  void onUserChangeTab(BottomBarItemType moduleId) {
    if (navigationSate.moduleIdSelected != moduleId) {
      navigationSate.moduleIdSelected = moduleId;
      navigationController.add(navigationSate);
    }
  }

  int get indexCurrent => getIndex(navigationSate.moduleIdSelected);

  int getIndex(BottomBarItemType moduleIdSelected) {
    return navigationSate.navigationItems
        .indexWhere((item) => item.layout == moduleIdSelected);
  }

  bool isInNavigationBar(BottomBarItemType layout) {
    return navigationSate.navigationItems.any((item) => item.layout == layout);
  }
}

class NavigationSate {
  BottomBarItemType moduleIdSelected;
  List<NavigationItem> navigationItems;

  NavigationSate(
      {required this.moduleIdSelected, required this.navigationItems});
}
