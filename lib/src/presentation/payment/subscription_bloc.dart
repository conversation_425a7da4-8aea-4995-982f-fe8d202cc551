import 'dart:async';

import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/base/bloc/base_bloc.dart';
import 'package:meditaguru/src/core/constants.dart';
// import 'package:in_app_purchase_ios/store_kit_wrappers.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/usecase/medita_usecase.dart';
import 'package:meditaguru/src/presentation/shared/account/account_bloc.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/handle_ui_bloc.dart';
import 'package:meditaguru/src/presentation/shared/app_bloc/loading_bloc.dart';
import 'package:rxdart/rxdart.dart';

class SubscriptionBloc extends BaseBloc {
  late LoadingBloc loadingBloc;
  late HandleUIBloc handleUIBloc;
  late MeditaUsecase meditaUsecase;

  final InAppPurchase _iap = InAppPurchase.instance;

  // final String id1 = 'month_1';
  // final String id2 = 'month_6';
  final String id3 = 'year_1';

  /// if the api is available or not.
  bool isAvailable = true;

  BehaviorSubject<List<ProductDetails?>> productsController =
      BehaviorSubject<List<ProductDetails>>();

  /// products for sale
  List<ProductDetails?> products = [];

  /// Update to purchases
  ProductDetails? selectedProduct;

  SubscriptionBloc() {
    loadingBloc = injector<LoadingBloc>();
    meditaUsecase = injector<MeditaUsecase>();
    handleUIBloc = HandleUIBloc();
  }

  @override
  void init() {
    _initialize();
  }

  bool isSelectedProduct(ProductDetails? product) {
    return selectedProduct == product;
  }

  bool selectProduct(ProductDetails? product) {
    selectedProduct = product;
    productsController.add(products);
    return true;
  }

  Future _initialize() async {
    isAvailable = await _iap.isAvailable();
    if (isAvailable) {
      await injector<AccountBloc>().completeOldTransaction();
      var futures = <Future>[
        getProducts(),
        injector<AccountBloc>().getGlobalConfig()
      ];
      await Future.wait(futures);
    }
  }

  Future completeOldTransaction() async {
    isAvailable = await _iap.isAvailable();
    if (isAvailable) {
      await injector<AccountBloc>().completeOldTransaction();
    }
  }

  @override
  void dispose() {}

  ///fetch products
  Future<void> getProducts() async {
    try {
      var ids = <String>{
        // id1,
        // id2,
        id3
      };
      var response = await _iap.queryProductDetails(ids);
      products = response.productDetails;
      print('response.productDetails ${response.productDetails.length}');

      //initial selected of products
      if (selectedProduct != null) {
        response.productDetails.forEach((element) {
          if (element.id == selectedProduct?.id) {
            selectedProduct = element;
          }
        });
      } else {
        if (response.productDetails.length >= 2) {
          selectedProduct = products[1];
        } else if (response.productDetails.length == 1) {
          selectedProduct = products[0];
        }
      }
    } catch (e) {
      products = [];
    } finally {
      productsController.add(products);
    }
  }

  ///buying a product
  void buyProduct() async {
    if (selectedProduct != null) {
      loadingBloc.loading();
      try {
        if (isIos) {
          isAvailable = await _iap.isAvailable();
          if (isAvailable) {
            await injector<AccountBloc>().completeOldTransaction();
            await getProducts();
          }
        }

        final purchaseParam = PurchaseParam(
            productDetails: selectedProduct!,
            applicationUserName: injector<AccountBloc>().auth.currentUser?.uid);
        await _iap.buyNonConsumable(purchaseParam: purchaseParam);
      } catch (e) {
        handleUIBloc.handleError(S.current.paymentFailed);
      } finally {
        loadingBloc.loaded();
      }
    } else {
      handleUIBloc.handleError('please select');
    }
  }

  String getInterval(ProductDetails? product) {
    return injector<AccountBloc>().getDuration(product?.id);
    // SKSubscriptionPeriodUnit periodUnit =
    //     product!.skProduct.subscriptionPeriod.unit;
    // int value = product.skProduct.subscriptionPeriod.numberOfUnits;
    // if (SKSubscriptionPeriodUnit.month == periodUnit) {
    //   return "${value} ${value > 1 ? S.current.months : S.current.month}";
    // } else if (SKSubscriptionPeriodUnit.week == periodUnit) {
    //   return "${value} ${value > 1 ? S.current.weeks : S.current.week}";
    // } else {
    //   return "${value} ${S.current.year}";
    // }
  }

  String getIntervalAndroid(ProductDetails? product) {
    return injector<AccountBloc>().getDuration(product?.id);
    // String durCode = product?.skuDetail.subscriptionPeriod.split("")[2];
    // int value = int.tryParse(product.skuDetail.subscriptionPeriod.split("")[1])??0;
    // if (durCode == "M") {
    //   return "${value} ${value > 1 ? S.current.months : S.current.month}";
    // } else if (durCode == "Y") {
    //   return "${value} ${S.current.year}";
    // } else {
    //   return "${value} ${value > 1 ? S.current.weeks : S.current.week}";
    // }
  }
}
