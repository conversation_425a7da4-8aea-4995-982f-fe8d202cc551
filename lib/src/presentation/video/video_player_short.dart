import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:meditaguru/src/core/base/bloc/base_state.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';

class VideoPlayerNormal extends StatefulWidget {
  final String url;

  VideoPlayerNormal({required this.url});

  @override
  _VideoPlayerNormalState createState() => _VideoPlayerNormalState();
}

class _VideoPlayerNormalState extends BaseState<VideoPlayerNormal> {
  late VideoPlayerController _controller;
  ChewieController? chewieController;

  String _url = '';

  @override
  void initState() {
    super.initState();
    SystemChrome.setPreferredOrientations(
        [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);
    _url = widget.url;

    if (_url.contains('http')) {
      _controller = VideoPlayerController.networkUrl(
        Uri.parse(_url),
      );
    } else {
      _controller = VideoPlayerController.asset(
        _url,
      );
    }

    chewieController = ChewieController(
      videoPlayerController: _controller,
      autoPlay: true,
      looping: false,
      deviceOrientationsAfterFullScreen: [DeviceOrientation.portraitUp],
      cupertinoProgressColors: ChewieProgressColors(
          playedColor: AppColors.secondary2Color,
          handleColor: AppColors.secondary2Color,
          backgroundColor: AppColors.whiteColor),
      materialProgressColors: ChewieProgressColors(
        playedColor: AppColors.secondary2Color,
        handleColor: AppColors.secondary2Color,
        backgroundColor: AppColors.whiteColor,
      ),
    );

    _controller.addListener(() {
      if (_isFirst &&
          chewieController != null &&
          chewieController!.videoPlayerController.value.isInitialized &&
          chewieController!.videoPlayerController.value.isPlaying) {
        chewieController!.pause();
        _isFirst = false;
      }
      setState(() {});
    });
  }

  bool _isFirst = true;

  @override
  void dispose() {
    _controller.dispose();
    chewieController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: const Key('video_player_short_key'),
      onVisibilityChanged: (VisibilityInfo info) {
        if (info.visibleFraction != 1 &&
            (chewieController?.isPlaying ?? false)) {
          chewieController?.pause();
        }
      },
      child: chewieController != null &&
              chewieController!.videoPlayerController.value.isInitialized
          ? Container(
              width: double.infinity,
              height: 300,
              child: Chewie(
                controller: chewieController!,
              ),
            )
          : Container(
              width: double.infinity,
              height: 300,
              child: const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: AppColors.secondary2Color,
                  ),
                ],
              ),
            ),
    );
  }
}
