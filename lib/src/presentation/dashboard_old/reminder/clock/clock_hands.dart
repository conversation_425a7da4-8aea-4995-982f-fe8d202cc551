import 'package:flutter/material.dart';

import 'hand_hour.dart';
import 'hand_minute.dart';
import 'hand_second.dart';

class ClockHands extends StatelessWidget {
  final DateTime dateTime;
  final bool showHourHandleHeartShape;

  ClockHands({required this.dateTime, this.showHourHandleHeartShape = false});

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1.0,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20.0),
        child: <PERSON>ack(
          fit: StackFit.expand,
          children: <Widget>[
            CustomPaint(
              painter: HourHand<PERSON>ainter(
                  hours: dateTime.hour, minutes: dateTime.minute),
            ),
            <PERSON><PERSON><PERSON>t(
              painter: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(
                  minutes: dateTime.minute, seconds: dateTime.second),
            ),
            Custom<PERSON><PERSON>t(
              painter: SecondHandPainter(seconds: dateTime.second),
            ),
          ],
        ),
      ),
    );
  }
}
