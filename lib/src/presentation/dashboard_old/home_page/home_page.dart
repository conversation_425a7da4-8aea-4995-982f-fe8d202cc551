import 'package:auto_size_text/auto_size_text.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_uxcam/flutter_uxcam.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/lib_common.dart';
import 'package:meditaguru/src/core/services/firebase/analytics/firebase_analytics_wapper.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/core/widgets/common/card_user_widget.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/entities/user/user_full_info.dart';
import 'package:meditaguru/src/domain/usecase/category_usecase.dart';
import 'package:meditaguru/src/domain/usecase/featured_stories_usecase.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';
import 'package:meditaguru/src/presentation/shared/account/account_bloc.dart';
import 'package:shimmer/shimmer.dart';

import '../../../core/utils.dart';
import '../../../core/widgets/common/banner_image/banner_image.dart';

class HomePageOld extends StatefulWidget {
  HomePageOld();

  @override
  _HomeState createState() => _HomeState();
}

class _HomeState extends State<HomePageOld> with WidgetsBindingObserver {
  FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;
  List featuredStoryList = [];
  List categoryList = [];

  @override
  void initState() {
    super.initState();
    refresh();
  }

  Future<bool> refresh() async {
    final featuredStoriesData =
        await injector.get<FeaturedStoriesUsecase>().getFeaturedStories();
    featuredStoryList
      ..clear()
      ..addAll(featuredStoriesData);

    if (mounted) setState(() {});

    final categories = await injector.get<CategoryUsecase>().getCategories();
    categoryList
      ..clear()
      ..addAll(categories);

    if (mounted) setState(() {});
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return MScaffoldPage(
      body: RefreshIndicator(
        color: AppColors.secondaryColor,
        backgroundColor: AppColors.primaryColor.withOpacity(0.5),
        onRefresh: refresh,
        child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Column(
              children: [
                buildWidget(),
                const SizedBox(
                  height: 40,
                )
              ],
            )),
      ),
    );
  }

  Container moreInfo() {
    return Container(
      width: 30.0,
      height: 30.0,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(7.0),
        gradient: const LinearGradient(
          begin: Alignment(0.85, -1.11),
          end: Alignment.bottomCenter,
          colors: [Color(0xFF414141), Color(0xFF0D0D0D)],
        ),
        border: Border.all(
          width: 1.0,
          color: const Color(0xFF707070),
        ),
      ),
      child: const Center(
        child: Text(
          '?',
          style: TextStyle(
            fontSize: 21.0,
            color: Colors.white,
            letterSpacing: 1.5000000457763671,
            height: 0.95,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Column buildWidget() {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          _buildProfile(),
          const SizedBox(height: 15),
          const BannerImage(),
          const SizedBox(height: 31),
          buildmeditationStep(),
          const SizedBox(height: 26),
          buildCategories(),
        ]);
  }

  StreamBuilder<UserFullInfo?> _buildProfile() {
    return StreamBuilder<UserFullInfo?>(
      stream: injector<AccountBloc>().userFullInfoController,
      builder: (context, snapshot) {
        var user = snapshot.data;
        return CardUserWidget.styleOld(user: user);
      },
    );
  }

  Positioned appBarWidget() {
    return Positioned(
      top: -10,
      height: 270,
      width: MediaQuery.sizeOf(context).width,
      child: Container(
        // color: AppColors.primaryColor,
        color: Colors.transparent,
        child: const Padding(
          padding: EdgeInsets.all(20.0),
          child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[]),
        ),
      ),
    );
  }

  InkWell buildGuidedMeditation() {
    return InkWell(
      onTap: () {
        context.startHtmlDetail(
            content: injector<AccountBloc>().globalConfig?.bannerLink);
      },
      child: Container(
          // height: MediaQuery.sizeOf(context).height * .28
          height: 200,
          width: MediaQuery.sizeOf(context).width - 40,
          padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
          decoration: BoxDecoration(
              image: const DecorationImage(
                  image: AssetImage('assets/img/bg/banner.png'),
                  alignment: Alignment.center,
                  fit: BoxFit.cover),
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                const BoxShadow(
                  color: Color.fromRGBO(12, 12, 12, .3),
                  blurRadius: 10,
                  offset: Offset(0, 10),
                )
              ]),
          child: Container()),
    );
  }

  Widget buildCategories() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: CText.title1(
              S.of(context).categories,
              fontWeight: FontWeight.bold,
              fontSize: 22,
            ),
          ),
          // Text(S.of(context).whatYourPriority,
          //     style: TextStyle(
          //         fontSize: 12, color: AppColors.textBackgroundColor)),
          const SizedBox(
            height: 15,
          ),
          AnimatedSwitcher(
            duration: const Duration(seconds: 2),
            child: (categoryList.isNotEmpty)
                ? LayoutBuilder(builder: (context, constraints) {
                    return GridView.builder(
                        shrinkWrap: true,
                        physics: const BouncingScrollPhysics(),
                        itemCount: categoryList.length,
                        gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                                childAspectRatio: 176 / 206,
                                crossAxisCount: 2,
                                mainAxisSpacing: 16,
                                crossAxisSpacing: 16),
                        itemBuilder: (BuildContext context, int index) {
                          DocumentSnapshot data = categoryList[index]['data'];
                          String coverImage = categoryList[index]['coverImage'];
                          return _categoryItem(data, coverImage);
                        });
                  })
                : Center(
                    child: SizedBox(
                      height: 100.0,
                      child: Shimmer.fromColors(
                        baseColor: AppColors.primaryColor,
                        highlightColor: AppColors.darkPrimaryColor,
                        child: Center(
                          child: Container(),
                        ),
                      ),
                    ),
                  ),
          )
        ],
      ),
    );
  }

  Widget buildmeditationStep() {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CText.title1(
                  S
                      .of(context)
                      .featuredStories
                      .replaceAll('Thiền Thức Tỉnh', ''),
                  fontWeight: FontWeight.normal,
                  fontSize: 22,
                ),
                CText.title1(
                  'Thiền Thức Tỉnh',
                  fontWeight: FontWeight.bold,
                  fontSize: 22,
                ),
              ],
            ),
          ),
          const SizedBox(
            height: 20,
          ),
          Container(
            height: 185,
            child: AnimatedSwitcher(
              duration: const Duration(seconds: 2),
              child: featuredStoryList.isNotEmpty
                  ? ListView.builder(
                      itemCount: featuredStoryList.length,
                      scrollDirection: Axis.horizontal,
                      physics: const BouncingScrollPhysics(),
                      itemBuilder: (context, index) {
                        return _storyItem(featuredStoryList[index]);
                      },
                    )
                  : SizedBox(
                      height: 180.0,
                      child: Shimmer.fromColors(
                          baseColor: Colors.white,
                          highlightColor: AppColors.darkPrimaryColor,
                          child: Container()),
                    ),
            ),
          )
        ],
      ),
    );
  }

  InkWell _storyItem(dynamic fsl) {
    var imageUrl = fsl['coverImage']?.toString().mediaLinkFirebase;
    return InkWell(
      onTap: () {
        FirebaseAnalyticsWapper().analytics.logEvent(
            name: 'Feature_story', parameters: {'storyItems': fsl['name']});
        if (injector<AccountBloc>().isUXCamInit) {
          FlutterUxcam.logEventWithProperties(
              'Feature_story', {'storyItems': fsl['name']});
        }

        context.startStories(fsl['storyItems']);
      },
      child: Container(
        margin: const EdgeInsets.only(left: 16, bottom: 5),
        alignment: Alignment.bottomLeft,
        width: 152.0,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.0),
          // image: DecorationImage(
          //   image: NetworkImage(imageUrl, scale: 152 / 180),
          //   fit: BoxFit.cover,
          // ),
          color: Colors.black,
          border: Border.all(
            width: 1.0,
            color: const Color(0xFF707070),
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF626262).withOpacity(0.6),
              offset: const Offset(2.0, 2.0),
              blurRadius: 6.0,
            ),
          ],
        ),
        child: Stack(
          children: [
            Positioned(
                bottom: 0,
                top: 0,
                right: 0,
                left: 0,
                child: LayoutBuilder(builder: (context, constrains) {
                  return ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: Tools.image(
                          url: imageUrl,
                          fit: BoxFit.cover,
                          width: constrains.maxWidth,
                          height: constrains.maxHeight));
                })),
            Positioned(
              bottom: 0,
              right: 0,
              left: 0,
              child: Container(
                width: 152.0,
                height: 38.0,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(20.0),
                    bottomRight: Radius.circular(16.0),
                    bottomLeft: Radius.circular(16.0),
                  ),
                  gradient: const LinearGradient(
                    begin: Alignment(0.0, -1.26),
                    end: Alignment.bottomCenter,
                    colors: [Color(0xFF414141), Colors.black],
                  ),
                  border: Border.all(
                    width: 0.1,
                    color: const Color(0xFF707070),
                  ),
                ),
                child: Row(
                  children: [
                    const SizedBox(
                      width: 10,
                    ),
                    Expanded(
                      flex: 1,
                      child: AutoSizeText(
                        fsl['name'] ?? '',
                        // 'margin: const EdgeInsets.symmetric(horizontal: 6),',
                        // overflow: TextOverflow.ellipsis,
                        style: CText.title2(
                          '',
                          // fontWeight: FontWeight.w700
                          fontWeight: FontWeight.w500,
                        ).style,
                      ),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _categoryItem(DocumentSnapshot data, String coverImage) {
    var imageUrl = coverImage.mediaLinkFirebase;
    return InkWell(
      onTap: () {
        FirebaseAnalyticsWapper().analytics.logEvent(
            name: 'Category',
            parameters: {'id': data['id'], 'name': data['name']});
        if (injector<AccountBloc>().isUXCamInit) {
          FlutterUxcam.logEventWithProperties(
              'Category', {'id': data['id'], 'name': data['name']});
        }
        context.startLessons(
            id: data['id'],
            coverImage: coverImage,
            name: data['name'],
            desc: (data.data()
                    as Map<String, dynamic>?)?['categoryDescription'] ??
                '');
      },
      child: Container(
        alignment: Alignment.bottomLeft,
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.0),
          // image: DecorationImage(
          //   image: NetworkImage(imageUrl, scale: 176/206),
          //   fit: BoxFit.cover,
          // ),
          color: Colors.black,
          border: Border.all(
            width: 1.0,
            color: const Color(0xFF707070),
          ),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF626262).withOpacity(0.6),
              offset: const Offset(2.0, 2.0),
              blurRadius: 6.0,
            ),
          ],
        ),
        child: Stack(
          children: [
            Positioned(
              bottom: 0,
              top: 0,
              right: 0,
              left: 0,
              child: LayoutBuilder(
                builder: (context, constrains) {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Tools.image(
                      url: imageUrl,
                      fit: BoxFit.cover,
                      width: constrains.maxWidth,
                      height: constrains.maxHeight,
                    ),
                  );
                },
              ),
            ),
            Positioned(
                bottom: 0,
                right: 0,
                left: 0,
                child: Container(
                  height: 52.0,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(20.0),
                      bottomRight: Radius.circular(16.0),
                      bottomLeft: Radius.circular(16.0),
                    ),
                    gradient: const LinearGradient(
                      begin: Alignment(0.0, -1.26),
                      end: Alignment.bottomCenter,
                      colors: [Color(0xFF414141), Colors.black],
                    ),
                    border: Border.all(
                      width: 0.1,
                      color: const Color(0xFF707070),
                    ),
                  ),
                  child: Row(
                    children: [
                      const SizedBox(
                        width: 10,
                      ),
                      Expanded(
                        flex: 1,
                        child: AutoSizeText(
                          data['name'],
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: CText.title2('',
                                  fontWeight: FontWeight.w500,
                                  fontSize: AppFontSize.normalFontSize2)
                              .style,
                        ),
                      ),
                      const SizedBox(
                        width: 4,
                      ),
                      Expanded(
                          flex: 0,
                          child: Container(
                            alignment: const Alignment(0.08, 0.0),
                            width: 28.5,
                            height: 28.5,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: const LinearGradient(
                                begin: Alignment(-0.64, -0.72),
                                end: Alignment(0.65, 0.7),
                                colors: [Color(0xFF2E3138), Color(0xFF1A1C1F)],
                              ),
                              border: Border.all(
                                width: 1.0,
                                color: const Color(0xFF27292F),
                              ),
                              boxShadow: [
                                const BoxShadow(
                                  color: Color(0xFF474D59),
                                  offset: Offset(-1.0, -2.0),
                                  blurRadius: 12.0,
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.arrow_forward_ios_rounded,
                              size: 14,
                              color: AppColors.secondary2Color,
                            ),
                          )),
                      const SizedBox(
                        width: 4,
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }
}

class LogoAnimation extends StatefulWidget {
  LogoAnimation({Key? key}) : super(key: key);

  @override
  _LogoAnimationState createState() => _LogoAnimationState();
}

class _LogoAnimationState extends State<LogoAnimation> {
  double padding = 60;

  @override
  void initState() {
    Future.delayed(const Duration(milliseconds: 100)).then((value) {
      setState(() {
        padding = 0;
      });
    });
    // setState(() {
    //   padding = 0;
    // });
    super.initState();
  }

  @override
  void dispose() {
    padding = 60;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          const SizedBox(width: double.infinity),
          AnimatedContainer(
            width: 240,
            height: 240,
            padding: EdgeInsets.all(padding),
            duration: const Duration(seconds: 2),
            curve: Curves.easeInOut,
            child: Container(
              // padding: EdgeInsets.all(10),
              width: double.infinity,
              child: Image.asset(
                'assets/img/round_logo.png',
                // height: 270,
                // width: 270,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
