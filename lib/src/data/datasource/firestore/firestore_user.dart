import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';

class FirestoreUserDataSource {
  final _firestore = FirebaseFirestore.instance;

  Future updateUserInfo({
    required String uid,
    required String phone,
    required String email,
    String? dob,
    String? photoUrl,
  }) async {
    await _firestore.collection('fl_content').doc(uid).set({
      'email': email,
      'dob': dob,
      'phone': phone,
      if (photoUrl?.isNotEmpty ?? false) 'photoUrl': photoUrl,
    }, SetOptions(merge: true));
  }

  Stream<QuerySnapshot<Map<String, dynamic>>> subscriptionRegisterUser(
      String uid) {
    return _firestore
        .collection('fl_content')
        .where('_fl_meta_.fl_id', isEqualTo: uid)
        .snapshots();
  }

  Future getUserSystem() async {
    var result = await _firestore
        .collection('fl_content')
        .where('_fl_meta_.fl_id', isEqualTo: 'ghHiijG9dQXk2MpCj2c87ovQIJy2')
        .get();

    return result;
  }

  Future<QuerySnapshot<Map<String, dynamic>>> getUser(String uid) async {
    var user = await FirebaseFirestore.instance
        .collection('fl_content')
        .where('_fl_meta_.fl_id', isEqualTo: uid)
        .get();
    return user;
  }

  // Future<QuerySnapshot<dynamic>?> getAllUser() async {
  // final data = await _firestore
  //     .collection('fl_content')
  //     .where('_fl_meta_.schema', isEqualTo: 'users')
  //     .get();

  // List<String> keyData = [];

  // final addData = data.docs.map((e) {
  //   return e.data().entries.map((e) {
  //     if (keyData.contains(e.key) == false && e.key != '_fl_meta_') {
  //       keyData.add(e.key);
  //     }
  //     return Map<String, dynamic>.from({e.key: e.value});
  //   }).toList();
  // }).toList();

  // final dataCSV = <List<String>>[];
  // final dataPurcharse = <List>[];

  // for (var i = 0; i < addData.length; i++) {
  //   final element = addData[i];
  //   final dataLine = <String>[];
  //   final plan = <String>[];

  //   final lastLogin = element.firstWhereOrNull((element) {
  //     return element.containsKey('lastLogin');
  //   })?['lastLogin'];
  //   if (lastLogin != null) {
  //     final dateTimeLastLogin = DateTime.tryParse(lastLogin);
  //     if (dateTimeLastLogin != null) {
  //       final diff = dateTimeLastLogin.compareTo(DateTime(2024, 1, 1));

  //       if (diff < 0) {
  //         print('----->> SKIP $dateTimeLastLogin');
  //         continue;
  //       }
  //     } else {
  //       print('----->> SKIP $lastLogin');
  //       continue;
  //     }
  //   } else {
  //     continue;
  //   }

  //   bool? isPremium;
  //   String? productId;
  //   String? transactionDate;

  //   for (var iii = 0; iii < keyData.length; iii++) {
  //     final valueItem = element
  //         .firstWhereOrNull((element) => element.containsKey(keyData[iii]));
  //     final valueLine = valueItem?.entries.first.value?.toString() ?? '';
  //     dataLine.add(valueLine);
  //     if (keyData[iii] == 'premiumPlan') {
  //       final premiumPlan = DateTime.tryParse(valueLine)?.toUtc().toLocal();

  //       if (premiumPlan != null && premiumPlan.isAfter(DateTime.now())) {
  //         isPremium = true;
  //       }
  //     } else if (keyData[iii] == 'listReceiptByDevice' && isPremium == null) {
  //       isPremium = false;
  //       final receipt =
  //           valueItem != null ? Map.from(valueItem.entries.first.value) : {};
  //       final purchasedItemsByDevice = receipt['purchasedItemsByDevice'];
  //       print(
  //           '------------->> isString:${purchasedItemsByDevice is String} isList:${purchasedItemsByDevice is List}');

  //       if (purchasedItemsByDevice != null &&
  //           purchasedItemsByDevice is List &&
  //           purchasedItemsByDevice.isNotEmpty) {
  //         final newList = purchasedItemsByDevice.toList();
  //         final listContent = [];
  //         for (var item in newList) {
  //           final purchaseItem = {};
  //           final originalJsonTemp = item?['originalJson'];
  //           if (originalJsonTemp != null) {
  //             if (originalJsonTemp is String) {
  //               if (originalJsonTemp.isNotEmpty) {
  //                 final originalJson = jsonDecode(originalJsonTemp);

  //                 if (originalJson['serverVerificationData'] != null &&
  //                     originalJson['serverVerificationData']
  //                         .toString()
  //                         .isNotEmpty) {
  //                   originalJson['serverVerificationData'] = '---';
  //                 }

  //                 if (originalJson['verificationDataLocal'] != null &&
  //                     originalJson['verificationDataLocal']
  //                         .toString()
  //                         .isNotEmpty) {
  //                   originalJson['verificationDataLocal'] = '---';
  //                 }
  //                 originalJson['hilelo'] = '---';
  //                 purchaseItem['originalJson'] = originalJson;

  //                 listContent.add(purchaseItem);
  //               }
  //             } else {
  //               listContent.add(item);
  //             }
  //           }
  //         }

  //         listContent.sort((a, b) {
  //           final aDate = a['originalJson']['transactionDate'];
  //           final bDate = b['originalJson']['transactionDate'];
  //           if (aDate == null) {
  //             return 0;
  //           }

  //           if (bDate == null) {
  //             return -1;
  //           }
  //           final dateTimeA = DateTime.tryParse(aDate);
  //           final dateTimeB = DateTime.tryParse(bDate);
  //           if (dateTimeA == null) {
  //             return 0;
  //           }

  //           if (dateTimeB == null) {
  //             return -1;
  //           }
  //           return dateTimeA.compareTo(dateTimeB);
  //         });

  //         dataPurcharse.add(listContent);

  //         final lastedPurchase =
  //             listContent.isNotEmpty ? listContent.last : {};

  //         try {
  //           final originalJson = lastedPurchase['originalJson'];

  //           productId = originalJson?['productID'];
  //           transactionDate = originalJson?['transactionDate'];

  //           final dateTimeTransaction =
  //               DateTime.tryParse(transactionDate ?? '');
  //           if (dateTimeTransaction != null) {
  //             final now = DateTime.now();
  //             final diff = now.difference(dateTimeTransaction);
  //             if (productId == 'month_1' && diff.inDays < 30) {
  //               isPremium = true;
  //             }

  //             if (productId == 'year_1' && diff.inDays < 365) {
  //               isPremium = true;
  //             }
  //           }
  //         } catch (e, t) {
  //           print(e.toString());
  //           print(t.toString());
  //         }
  //       }
  //     }
  //   }

  //   plan.addAll([
  //     productId?.toString() ?? '',
  //     transactionDate?.toString() ?? '',
  //     '$isPremium',
  //   ]);
  //   // print('---> addData $i');
  //   dataLine.addAll(plan);
  //   dataCSV.add(dataLine);
  // }

  // dataCSV.sort((a, b) {
  //   final aDate = a.last;
  //   final bDate = b.last;
  //   final dateTimeA = DateTime.tryParse(aDate);
  //   final dateTimeB = DateTime.tryParse(bDate);
  //   if (dateTimeA == null) {
  //     return 0;
  //   }

  //   if (dateTimeB == null) {
  //     return -1;
  //   }

  //   return dateTimeB.compareTo(dateTimeA);
  // });

  // keyData.addAll([
  //   'Last productID',
  //   'Last transactionDate',
  //   'isPremium',
  // ]);

  // dataCSV.insert(0, keyData);

  // final csv = const ListToCsvConverter().convert(dataCSV);
  // final f = File(
  //     '/Users/<USER>/Downloads/medita/user-${DateTime.now().millisecondsSinceEpoch}.csv');
  // final isExistFile = await f.exists();
  // if (isExistFile == false) {
  //   await f.create(recursive: true);
  // }
  // var excelCsvBytes = <int>[];
  // excelCsvBytes = [0xEF, 0xBB, 0xBF, ...utf8.encode(csv)];

  // await f.writeAsBytes(excelCsvBytes, mode: FileMode.append);

  // final fJSON = File(
  //     '/Users/<USER>/Downloads/medita/user-${DateTime.now().millisecondsSinceEpoch}.json');
  // await fJSON.create(recursive: true);
  // await fJSON.writeAsString(jsonEncode(dataPurcharse));

  // return data;

  //   return null;
  // }

  Future<DocumentSnapshot<Map<String, dynamic>>> getUserByDocId(
      String uid) async {
    var user = await _firestore.collection('fl_content').doc('$uid').get();
    return user;
  }
}
