import 'package:cloud_firestore/cloud_firestore.dart';

abstract class UserRepo {
  // Future<User> login(String username, String password);
  // Future<User> getUserInfo(cookie);
  // Future<User> createUser(
  //     {phone, email, firstName, lastName, password, int gender, String dob});
  // Future<bool> sendOTP(String phoneNumber);
  // Future<bool> verifyOTP(String phoneNumber, String code);
  // Future<bool> changePassword(
  //     String currentPassword, String newPassword, String token);

  Future getUserSystem();
  Future<DocumentSnapshot<Map<String, dynamic>>> getUserByDocId(String uid);
  Future<QuerySnapshot<Map<String, dynamic>>> getUser(String uid);

  Future updateUserInfo({
    required String uid,
    required String phone,
    required String email,
    String? dob,
    String? photoUrl,
  });

  // Future getAllUsers();
}
