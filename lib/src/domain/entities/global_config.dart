class GlobalConfig {
  String? termsAndConditions;
  String? privacyPolicy;
  String? bannerLink;
  bool? isDisplayBankMethodPayment;
  String? transferMoneyInfoVi;
  String? transferMoneyInfoEn;
  bool? hideFeaturesIos;
  int? storiesDuration;
  bool? hideFacebookLogin;

  GlobalConfig({
    this.termsAndConditions,
    this.privacyPolicy,
    this.bannerLink,
    this.isDisplayBankMethodPayment,
    this.transferMoneyInfoVi,
    this.transferMoneyInfoEn,
    this.hideFeaturesIos,
    this.storiesDuration,
    this.hideFacebookLogin,
  });
}
