import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/utils/logs.dart';
import 'package:meditaguru/src/presentation/dashboard/advanced_meditation/advanced_meditation_screen.dart';
import 'package:meditaguru/src/presentation/dashboard/home/<USER>';
import 'package:meditaguru/src/presentation/dashboard_old/home_page/home_page.dart';
import 'package:meditaguru/src/presentation/dashboard_old/settings/setting.dart';

import '../../presentation/dashboard_old/reminder/alarm.dart';
import 'category_meditation.dart';

enum BottomBarItemType {
  home,
  alarm,
  advancedMeditation,
  settings;

  bool get isHome => this == home;

  factory BottomBarItemType.fromString(String? value) {
    switch (value) {
      case 'alarm':
        return alarm;
      case 'advancedMeditation':
        return advancedMeditation;
      case 'settings':
        return settings;
      case 'home':
        return home;
      default:
        return home;
    }
  }

  String title(BuildContext context) {
    switch (this) {
      case home:
        return 'Thiền thức tỉnh';
      case alarm:
        return 'Hẹn giờ thiền';
      case advancedMeditation:
        return 'Thiền nâng cao';
      case settings:
        return '<PERSON><PERSON> sơ';
    }
  }

  Widget build(
      BuildContext context, ScrollController scrollController, Map? data) {
    switch (this) {
      case home:
        return HomePage(scrollController: scrollController);
      case alarm:
        return Alarm();
      case advancedMeditation:
        var listCourses = <CourseMeditation>[];
        final courses = data?['courses'];

        if ((data?.isNotEmpty ?? false) && courses != null && courses is List)
          try {
            listCourses = courses
                .map((e) =>
                    CourseMeditation.fromJson(Map<String, dynamic>.from(e)))
                .toList();
          } catch (e) {
            printError(e);
          }

        return AdvancedMeditaionPage(
          scrollController: scrollController,
          listCourses: listCourses,
        );
      case settings:
        return Setting();
    }
  }

  Widget buildOld(BuildContext context) {
    switch (this) {
      case home:
        return HomePageOld();
      case alarm:
        return Alarm();
      case advancedMeditation:
      case settings:
        return Setting();
    }
  }
}
