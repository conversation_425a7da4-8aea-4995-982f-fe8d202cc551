import 'space_size.dart';

class DynamicTextData {
  final String text;
  final bool pinned;
  final bool center;
  final double height;
  final SpaceSize? padding;
  final SpaceSize? margin;

  DynamicTextData({
    required this.text,
    this.pinned = false,
    this.center = false,
    this.height = 60.0,
    this.padding,
    this.margin,
  });

  Map<String, dynamic> toJson() {
    final result = <String, dynamic>{}
      ..addAll({'text': text})
      ..addAll({'pinned': pinned})
      ..addAll({'center': center})
      ..addAll({'padding': padding?.toJson()})
      ..addAll({'margin': margin?.toJson()})
      ..addAll({'height': height});

    return result;
  }

  factory DynamicTextData.fromJson(Map<String, dynamic> map) {
    return DynamicTextData(
        text: map['text'] ?? '',
        pinned: map['pinned'] ?? false,
        center: map['center'] ?? true,
        padding: map['padding'] != null
            ? SpaceSize.fromJson(Map<String, dynamic>.from(map['padding']))
            : map['padding'],
        margin: map['margin'] != null
            ? SpaceSize.fromJson(Map<String, dynamic>.from(map['margin']))
            : map['margin'],
        height: (num.tryParse(map['height'].toString()) ?? 60).toDouble());
  }
}
