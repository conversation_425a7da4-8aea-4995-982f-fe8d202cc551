import 'package:flutter/material.dart';
import 'package:meditaguru/src/core/lib_common.dart';

class MDividerVertical extends StatelessWidget {
  final Color color;
  final double indent;
  final double endIndent;

  const MDividerVertical(
      {Key? key,
      this.color = AppColors.dividerColor,
      this.indent = 0,
      this.endIndent = 0})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return VerticalDivider(
      color: color,
      width: 0.5,
      thickness: 1,
      indent: indent,
      endIndent: endIndent,
    );
  }
}

class MDivider extends StatelessWidget {
  final Color color;
  final double indent;
  final double endIndent;
  final double height;

  const MDivider(
      {Key? key,
      this.color = AppColors.dividerColor,
      this.indent = 0,
      this.endIndent = 0,
      this.height = 1})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Divider(
      color: color,
      height: height,
      thickness: height,
      indent: indent,
      endIndent: endIndent,
    );
  }
}
