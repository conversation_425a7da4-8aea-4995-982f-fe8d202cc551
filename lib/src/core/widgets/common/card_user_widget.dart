import 'package:flutter/material.dart';
import 'package:meditaguru/generated/l10n.dart';
import 'package:meditaguru/src/core/utils/tools.dart';
import 'package:meditaguru/src/core/widgets/base_widgets/lib_base_widgets.dart';
import 'package:meditaguru/src/domain/entities/user/user_full_info.dart';
import 'package:meditaguru/src/presentation/app_coodinator.dart';

const _kOldStyleTitle = TextStyle(
  fontSize: 24.0,
  fontFamily: 'MyFont2',
  color: Color(0xFFF6FC9C),
  fontWeight: FontWeight.w700,
  // height: 1.67,
);

const _kOldStyleUsername = TextStyle(
  fontFamily: 'MyFont2',
  fontSize: 17.0,
  color: Colors.white,
  fontWeight: FontWeight.w700,
  // height: 2.35,
);

class CardUserWidget extends StatelessWidget {
  final UserFullInfo? user;
  final Color? color;
  final double size;
  final double radius;
  final TextStyle styleTitle;
  final TextStyle styleUsername;

  final bool _useOldStyle;

  const CardUserWidget({
    super.key,
    this.user,
    this.color,
    this.size = 40,
    this.radius = 12,
    this.styleTitle = const TextStyle(
      fontFamily: 'MyFont3',
      fontSize: 14.0,
      color: Color(0xFFD0D5DD),
      fontWeight: FontWeight.w500,
      letterSpacing: 1.00,
    ),
    this.styleUsername = const TextStyle(
      fontSize: 17.0,
      fontFamily: 'MyFont3',
      color: Colors.white,
      fontWeight: FontWeight.w500,
      letterSpacing: 1.00,
    ),
  }) : _useOldStyle = false;

  CardUserWidget.styleOld({
    super.key,
    this.user,
    this.color,
    this.size = 60,
    this.radius = 16,
    this.styleTitle = _kOldStyleTitle,
    this.styleUsername = _kOldStyleUsername,
  }) : _useOldStyle = true;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        context.startProfile();
      },
      child: Container(
        color: color,
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              flex: 0,
              child: Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(radius),
                  color: const Color(0xFF707070),
                  border: Border.all(
                    width: 1.0,
                    color: const Color(0xFF707070),
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(radius),
                  child: Tools.image(
                    url: user?.photoUrl != null
                        ? Tools.getHigherResProviderPhotoUrl(
                            user?.photoUrl ?? '')
                        : 'assets/img/ic_launcher.png',
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            MBlock(12),
            Expanded(
              flex: 1,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: <Widget>[
                  Text('${S.of(context).hello}', style: styleTitle),
                  if (_useOldStyle) const SizedBox(height: 8),
                  Text(
                    user?.name ?? S.of(context).fullName,
                    style: styleUsername,
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
