import 'package:flutter/material.dart';

class TextBackground extends StatelessWidget {
  final Widget? child;
  final bool isFitWidth;
  final double circle;

  const TextBackground(
      {Key? key, this.child, this.isFitWidth = true, this.circle = 10})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: isFitWidth ? double.infinity : null,
      padding: const EdgeInsets.only(top: 10, left: 10, right: 10, bottom: 10),
      decoration: BoxDecoration(
        color: Colors.black54,
        gradient: linearGradient,
        borderRadius: BorderRadius.circular(circle),
      ),
      child: child,
    );
  }

  static LinearGradient get linearGradient {
    return const LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Colors.black54, Colors.black26]);
  }
}
