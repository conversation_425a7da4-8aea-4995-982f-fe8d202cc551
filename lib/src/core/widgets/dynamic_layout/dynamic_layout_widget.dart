import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:meditaguru/meditaguru.dart';
import 'package:meditaguru/src/core/extensions/map_extension.dart';
import 'package:meditaguru/src/core/widgets/common/banner_image/banner_image_register_premium_widget.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/domain/entities/dynamic_text_data.dart';
import 'package:meditaguru/src/presentation/shared/daily_inspiration/daily_inspiration_model.dart';

import '../../../presentation/shared/account/account_bloc.dart';
import '../../../presentation/shared/account/widgets/user_card_widget.dart';
import '../../wrappers/sliver_section_widget.dart';
import '../common/draw_card_widget.dart';
import '../header_widget.dart';

class SliverDynamicLayoutWidget extends StatefulWidget {
  const SliverDynamicLayoutWidget({
    super.key,
    required this.configs,
  });

  final Map<String, dynamic> configs;

  @override
  State<SliverDynamicLayoutWidget> createState() =>
      _SliverDynamicLayoutWidgetState();
}

class _SliverDynamicLayoutWidgetState extends State<SliverDynamicLayoutWidget> {
  String get layout => widget.configs['layout'] ?? '';
  Map<String, dynamic> get configs => widget.configs;
  @override
  Widget build(BuildContext context) {
    try {
      switch (layout) {
        case 'userCard':
          final config = configs.toDynamicConfigBasic();
          return SliverPadding(
            padding: config?.margin?.toEdgeInsets() ?? EdgeInsets.zero,
            sliver: const SliverToBoxAdapter(
              child: UserCardWidget(),
            ),
          );
        case 'space':
          final config = configs.toDynamicConfigBasic();
          final percentHeight =
              (num.tryParse(config?.percentHeight?.toString() ?? '') ?? 28)
                  .toDouble();

          return SliverToBoxAdapter(
            child: SizedBox(
              height: MediaQuery.sizeOf(context).height * (percentHeight / 100),
            ),
          );

        case 'text':
          final textData = DynamicTextData.fromJson(configs['config']);

          return SliverPadding(
            padding: textData.margin?.toEdgeInsets() ?? EdgeInsets.zero,
            sliver: SliverAppBar(
              backgroundColor: Colors.transparent,
              toolbarHeight: textData.height,
              centerTitle: textData.center,
              pinned: textData.pinned,
              snap: textData.pinned,
              floating: textData.pinned,
              elevation: 10,
              title: HeaderWidget(
                text: textData.text,
                maxLines: 4,
              ),
            ),
          );
        case 'banner':
          final bannerData = List<Map<String, dynamic>>.from(configs['items'])
              .map(CategoryMeditation.fromJson)
              .toList();

          return SliverToBoxAdapter(
            child: SliderIssuesYourCareWidget(
              categories: bannerData,
              onPressed: (category) {
                context.startDetailCategoryMeditation(category);
              },
            ),
          );
        case 'drawCard':
          final config = configs.toDynamicConfigBasic();
          final hasData = context
              .read<DailyInspirationModel>()
              .listDailyInspiration
              .isNotEmpty;
          if (hasData) {
            return SliverPadding(
              padding: config?.margin?.toEdgeInsets() ?? EdgeInsets.zero,
              sliver: const SliverToBoxAdapter(
                child: DrawCardWidget(),
              ),
            );
          }

          return const SliverToBoxAdapter();

        case 'bannerRegisterPremium':
          final config = configs.toDynamicConfigBasic();

          return SliverPadding(
            padding: config?.margin?.toEdgeInsets() ?? EdgeInsets.zero,
            sliver: SliverToBoxAdapter(
              child: StreamBuilder<User?>(
                stream: injector<AccountBloc>().userController,
                builder: (_, __) {
                  final isPremium =
                      context.read<AccountBloc>().isBothPuchasedPremium;

                  if (isPremium == false) {
                    return const BannerImageRegisterPremiumWidget();
                  }

                  return const SizedBox();
                },
              ),
            ),
          );

        case 'breathingExcercises':
          final config = configs.toDynamicConfigBasic();

          return SliverPadding(
            padding: config?.margin?.toEdgeInsets() ?? EdgeInsets.zero,
            sliver: SliverSectionWidget(
              header: Padding(
                padding: const EdgeInsets.only(top: 20, bottom: 20),
                child: Row(
                  children: [
                    const HeaderWidget(
                      text: 'Tập thở',
                      maxLines: 4,
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      height: 30,
                      child: ButtonGradiantWidget(
                        paddingContent:
                            const EdgeInsets.symmetric(horizontal: 20),
                        onPressed: () {},
                        colors: [
                          const Color(0xffE4CF13),
                          const Color(0xffF6EA7E),
                        ],
                        title: 'Mới',
                      ),
                    ),
                  ],
                ),
              ),
              sliver: const SliverToBoxAdapter(
                child: CardBreathingExcercisesWidget(
                  timeMinute: 2,
                  imageUrl: 'assets/img/breathing_banner.png',
                ),
              ),
            ),
          );

        case 'sharingForYou':
          final config = configs.toDynamicConfigBasic();
          final sharedData = List<Map<String, dynamic>>.from(configs['items'])
              .map(SharingForYour.fromJson)
              .toList();

          return SliverToBoxAdapter(
            child: SharingForYouWidget(
              infosShared: sharedData,
              configBasic: config,
            ),
          );

        default:
          return const SliverToBoxAdapter();
      }
    } catch (e) {
      return const SliverToBoxAdapter();
    }
  }
}
