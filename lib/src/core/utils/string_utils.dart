import 'package:intl/intl.dart';

class StringUtils {
  static String enumToName(String enumToString) {
    final paths = enumToString.split('.');
    return paths[1];
  }

  static String convertToCurrency(dynamic price) {
    if (price is String || !(price is num)) {
      price = double.tryParse(price) ?? 0;
    }
    return currencyFormat.format(price); // + "đ";
  }

  static String convertToNormalNumber(dynamic price) {
    if (price is String || !(price is num)) {
      price = double.tryParse(price) ?? 0;
    }
    return NumberFormat('0.##').format(price); // + "đ";
  }

  static String convertToNumber(dynamic price) {
    if (price is String || !(price is num)) {
      price = double.tryParse(price) ?? 0;
    }
    return NumberFormat('#,##0.##').format(price); // + "đ";
  }

  static String convertToThoundsand(dynamic price) {
    try {
      if (price is String) {
        price = double.parse(price);
      }
      return currencyFormat.format(price);
    } catch (e) {
      return price;
    }
  }

  static String convertToThoundsandK(dynamic price) {
    try {
      if (price is String) {
        price = double.parse(price);
      }
      var k = '';
      if (price >= 1000) {
        price = price / 1000;
        k = 'k';
      }
      return '${NumberFormat("#,##0.##").format(price)}$k';
    } catch (e) {
      return price;
    }
  }

  static double getReverseValue(String value, bool isIgnore) {
    if (isIgnore) {
      return double.parse(value);
    }
    try {
      return double.parse(value) * -1;
    } catch (e) {
      return 0;
    }
  }

  static String? removeDecimalIfNeeded(dynamic value, {int decimal = 1}) {
    if (value is String) {
      try {
        value = double.parse(value);
      } catch (e) {
        return value;
      }
    }
    if (value is double) {
      if (value % 1 == 0) {
        return value.toInt().toString();
      }
      return value.toStringAsFixed(decimal);
    }
    return null;
  }

  static bool isNotEmpty(dynamic item) {
    if (item == null) return false;
    if (item is String) {
      return item.isNotEmpty;
    }
    return false;
  }

  static bool isEmpty(String? s) {
    return !isNotEmpty(s);
  }

  static final currencyFormat = NumberFormat('#,##0.## đ', 'vi_VN');

  static String getAssetPath(String? asset) {
    return 'assets/$asset';
  }
}
