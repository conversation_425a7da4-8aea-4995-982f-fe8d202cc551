import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class SystemLifecycleListener {
  static final instance = SystemLifecycleListener();

  final _callbacks = <AppLifeCycleCallback>{};

  void _invokeCallbacks(AppLifeCycle lifeCycle) {
    for (var callback in _callbacks) {
      callback(lifeCycle);
    }
  }

  void addCallback(AppLifeCycleCallback callback) {
    _callbacks.add(callback);
  }

  void removeCallback(AppLifeCycleCallback callback) {
    _callbacks.remove(callback);
  }

  SystemLifecycleListener() {
    setHandler();
  }

  void setHandler() {
    SystemChannels.lifecycle.setMessageHandler((msg) async {
      if (msg.toString() == AppLifecycleState.resumed.toString()) {
        print('SystemLifecycleListener: resumed');
        _invokeCallbacks(AppLifeCycle.onResume);
      } else if (msg.toString() == AppLifecycleState.paused.toString()) {
        print('SystemLifecycleListener: paused');
        _invokeCallbacks(AppLifeCycle.onPause);
      } else if (msg.toString() == AppLifecycleState.inactive.toString()) {
        print('SystemLifecycleListener: inactive');
        _invokeCallbacks(AppLifeCycle.onPause);
      } else if (msg.toString() == AppLifecycleState.detached.toString()) {
        print('SystemLifecycleListener: detached');
        _invokeCallbacks(AppLifeCycle.detached);
      }
      return '';
    });
  }
}

enum AppLifeCycle { onPause, onResume, detached }

typedef AppLifeCycleCallback = void Function(AppLifeCycle lifeCycle);
