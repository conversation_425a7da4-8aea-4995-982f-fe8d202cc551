// ignore_for_file: deprecated_member_use

import 'dart:async';

import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';

abstract class DeeplinkServiceDelegate {
  void receiveInviteCode(String code);
}

class DeeplinkService {
  DeeplinkServiceDelegate? delegate;

  void fetchLinkData() async {
    // FirebaseDynamicLinks.getInitialLInk does a call to firebase to get us the real link because we have shortened it.
    var link = await FirebaseDynamicLinks.instance.getInitialLink();

    // This link may exist if the app was opened fresh so we'll want to handle it the same way onLink will.
    handleLinkData(link);

    // This will handle incoming links if the application is already opened
    FirebaseDynamicLinks.instance.onLink.listen(
        (PendingDynamicLinkData? dynamicLink) async {
      handleLinkData(dynamicLink);
    }, onError: (e) async {
      print('Deeplink error : ${e.toString()}');
      return;
    });
  }

  void handleLinkData(PendingDynamicLinkData? data) async {
    final uri = data?.link;
    if (uri != null) {
      final queryParams = uri.queryParameters;
      if (queryParams.isNotEmpty) {
        if (queryParams.containsKey('invitecode')) {
          var userName = queryParams['invitecode'] ?? '';
          // verify the username is parsed correctly
          // print("My users username is: $userName  path: ${uri.path}");
          delegate?.receiveInviteCode(userName);
          // _handleUIBloc.handleSuccess("My users username is: $userName  path: ${uri.path}");
        }
      }
    }
  }

  static Future<Uri> createDynamicLink({required String userId}) async {
    var parameters = DynamicLinkParameters(
      // This should match firebase but without the username query param
      uriPrefix: 'https://thienthuctinh.page.link',
      // This can be whatever you want for the uri, https://yourapp.com/groupinvite?username=$userName
      link: Uri.parse('https://inapps.net/signup?invitecode=$userId'),
      androidParameters: const AndroidParameters(
        packageName: 'com.inapps.medita',
        minimumVersion: 1,
      ),
      iosParameters: const IOSParameters(
        bundleId: 'com.inapps.medita',
        minimumVersion: '1.0.0',
        appStoreId: '1547936055',
      ),
    );
    final shortDynamicLink = await FirebaseDynamicLinks.instance.buildShortLink(
      parameters,
      shortLinkType: ShortDynamicLinkType.short,
    );
    return shortDynamicLink.shortUrl;
  }
}
