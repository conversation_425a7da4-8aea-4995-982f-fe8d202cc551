import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:meditaguru/src/core/utils.dart';

class FCMDebugHelper {
  static Future<void> debugFCMStatus() async {
    if (!kDebugMode) return;
    
    try {
      Log.printSimpleLog('=== FCM Debug Status ===');
      
      // Check if Firebase is initialized
      final messaging = FirebaseMessaging.instance;
      Log.printSimpleLog('Firebase Messaging instance: ${messaging.toString()}');
      
      // Check notification settings
      final settings = await messaging.getNotificationSettings();
      Log.printSimpleLog('Authorization status: ${settings.authorizationStatus}');
      Log.printSimpleLog('Alert setting: ${settings.alert}');
      Log.printSimpleLog('Badge setting: ${settings.badge}');
      Log.printSimpleLog('Sound setting: ${settings.sound}');
      Log.printSimpleLog('Provisional setting: ${settings.provisional}');
      
      // Try to get token
      try {
        final token = await messaging.getToken();
        Log.printSimpleLog('FCM Token available: ${token != null}');
        if (token != null) {
          Log.printSimpleLog('Token length: ${token.length}');
          Log.printSimpleLog('Token preview: ${token.substring(0, 20)}...');
        }
      } catch (e) {
        Log.printSimpleLog('Error getting FCM token: $e');
      }
      
      // Check APNS token (iOS specific)
      try {
        final apnsToken = await messaging.getAPNSToken();
        Log.printSimpleLog('APNS Token available: ${apnsToken != null}');
        if (apnsToken != null) {
          Log.printSimpleLog('APNS Token length: ${apnsToken.length}');
        }
      } catch (e) {
        Log.printSimpleLog('Error getting APNS token: $e');
      }
      
      Log.printSimpleLog('=== End FCM Debug ===');
    } catch (e) {
      Log.printSimpleLog('FCM Debug failed: $e');
    }
  }
  
  static Future<void> requestPermissionsWithDebug() async {
    try {
      Log.printSimpleLog('Requesting FCM permissions...');
      final messaging = FirebaseMessaging.instance;
      
      final settings = await messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: true,
        sound: true,
      );
      
      Log.printSimpleLog('Permission request result: ${settings.authorizationStatus}');
      
      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        Log.printSimpleLog('FCM permissions granted successfully');
      } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
        Log.printSimpleLog('FCM provisional permissions granted');
      } else {
        Log.printSimpleLog('FCM permissions denied: ${settings.authorizationStatus}');
      }
      
    } catch (e) {
      Log.printSimpleLog('Error requesting FCM permissions: $e');
    }
  }
}
