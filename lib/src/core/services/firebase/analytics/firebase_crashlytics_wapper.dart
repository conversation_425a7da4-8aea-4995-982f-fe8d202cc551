import 'dart:isolate';

import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart' show FlutterError, kDebugMode;
import 'package:meditaguru/src/core/services/device_info_service.dart';

class FirebaseCrashlyticsService {
  // setup:
  // https://firebase.google.com/docs/crashlytics/get-started?authuser=5&platform=ios

  static Future<void> init(DeviceInfoModel deviceInfo) async {
    if (kDebugMode) {
      // Force disable Crashlytics collection while doing every day development.
      // Temporarily toggle this to true if you want to test crash reporting in your app.
      // await FirebaseCrashlytics.instance
      //     .setCrashlyticsCollectionEnabled(false);

      await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
    } else {
      // Handle Crashlytics enabled status when not in Debug,
      // e.g. allow your users to opt-in to crash reporting.
    }

    // Guest
    var appInfo = deviceInfo;
    await FirebaseCrashlytics.instance
        .setUserIdentifier(appInfo.deviceId ?? '');

    // Errors outside of Flutte
    // To catch errors that happen outside of the Flutter context, install an error listener on the current Isolate:
    Isolate.current.addErrorListener(RawReceivePort((pair) async {
      final List<dynamic> errorAndStacktrace = pair;
      await FirebaseCrashlytics.instance.recordError(
        errorAndStacktrace.first,
        errorAndStacktrace.last,
      );
    }).sendPort);

    // Pass all uncaught errors from the framework to Crashlytics.
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;

    // Add custom log messages
    // await FirebaseCrashlytics.instance.log("Hello app !");

    // Forcing a crash
    // FirebaseCrashlytics.instance.crash();
  }

  static Future<void> setUserIdentifier(String id) async {
    if (id.isNotEmpty) {
      await FirebaseCrashlytics.instance.setUserIdentifier(id);
    }
  }
}
