import 'package:meditaguru/src/core/interceptor/token_interceptor.dart';
import 'package:meditaguru/src/core/routes/route_observer.dart';
import 'package:meditaguru/src/di/injection/injection.dart';
import 'package:meditaguru/src/presentation/shared/account/account_bloc.dart';

import '../utils.dart';
import 'api_code.dart';

class CustomException implements Exception {
  int? errorCode;
  String? errorMessage = '';
  String? stackTrace = '';

  @override
  String toString() => (errorMessage ?? 'Unexpected error occured').toString();

  CustomException(
      {this.errorCode = FAILED,
      this.errorMessage = 'Unexpected error occured',
      Object? exception}) {
    stackTrace = StackTrace.current.toString();

    if (exception is TypeError) {
      errorCode = kTypeError;
      errorMessage = 'Invalid received data: $exception';
    } else if (exception is NoSuchMethodError) {
      errorMessage = exception.toString();
      errorCode = FAILED;
    } else if (!(exception is CustomException)) {
      switch (errorCode) {
        case NOT_FOUND:
          errorMessage = 'not found';
          break;
        case FAILED:
          errorMessage = 'error';
          break;
      }
    }

    if (exception is CustomException) {
      errorCode = exception.errorCode;
      errorMessage = exception.errorMessage;
    } else {
      Log.printSimpleLog(
          'Screen ${MyRouteObserver.currentRootName} : $errorMessage');
      Log.printSimpleLog('===== start stacktrace');
      Log.printSimpleLog('$stackTrace');
      Log.printSimpleLog('===== end stacktrace');
    }
    if (TokenInterceptor.unauthorizedTokenCodes.contains(errorCode)) {
      injector<AccountBloc>().logout();
    }
  }
}
