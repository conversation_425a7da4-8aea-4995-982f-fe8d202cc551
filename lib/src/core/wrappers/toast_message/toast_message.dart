import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

import 'widgets/in_app_notification.dart';

enum ToastMessageType {
  success,
  warning,
  error,
}

extension ShowToastExt on State<StatefulWidget> {
  void showToastMessage(String message,
      [ToastMessageType type = ToastMessageType.success]) {
    var colorBg = Theme.of(context).scaffoldBackgroundColor;
    var icon = const Icon(CupertinoIcons.checkmark_circle, color: Colors.black);

    switch (type) {
      case ToastMessageType.success:
        colorBg = Colors.white;
        icon = const Icon(CupertinoIcons.checkmark_circle, color: Colors.black);
        break;
      case ToastMessageType.warning:
        colorBg = Colors.amber[400]!;
        icon = const Icon(CupertinoIcons.info, color: Colors.white);
        break;
      case ToastMessageType.error:
        colorBg = const Color(0xffff5252);
        icon = const Icon(Icons.error, color: Colors.white);
        break;
    }

    showTopSnackBar(
      context,
      InAppNotificationWidget(
        text: message,
        colorBackground: colorBg,
        icon: icon,
        textStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: ToastMessageType.success == type
                  ? Colors.black
                  : Colors.white,
            ),
      ),
      displayDuration: const Duration(milliseconds: 1200),
    );
  }

  void showText(String message) {
    BotToast.showText(text: message);
  }
}

extension ObjectCopy on Object {
  void copyText(String? text) {
    Clipboard.setData(ClipboardData(text: text ?? '')).then((value) {
      // showToastText(message ?? 'Đã sao chép');
      BotToast.showText(text: 'Copied');
    });
  }
}

typedef ToastBuilder = Widget Function(BuildContext, Widget?);
final ToastBuilder toastBuilder = BotToastInit();

extension BuildContextShowTostMessage on BuildContext {
  void showToastMessage(String message,
      [ToastMessageType type = ToastMessageType.success]) {
    var colorBg = Theme.of(this).scaffoldBackgroundColor;
    var icon = const Icon(CupertinoIcons.checkmark_circle, color: Colors.black);

    switch (type) {
      case ToastMessageType.success:
        colorBg = Colors.white;
        icon = const Icon(CupertinoIcons.checkmark_circle, color: Colors.black);
        break;
      case ToastMessageType.warning:
        colorBg = Colors.amber[400]!;
        icon = const Icon(CupertinoIcons.info, color: Colors.white);
        break;
      case ToastMessageType.error:
        colorBg = const Color(0xffff5252);
        icon = const Icon(Icons.error, color: Colors.white);
        break;
    }

    showTopSnackBar(
      this,
      InAppNotificationWidget(
        text: message,
        colorBackground: colorBg,
        icon: icon,
        textStyle: Theme.of(this).textTheme.bodyLarge?.copyWith(
              color: ToastMessageType.success == type
                  ? Colors.black
                  : Colors.white,
            ),
      ),
      displayDuration: const Duration(milliseconds: 1200),
    );
  }
}

extension BuctxLoading on BuildContext {
  void showLoading() {
    BotToast.showLoading();
  }

  void hideLoading() {
    BotToast.closeAllLoading();
  }
}

// final animationBuilder = (context, animation, child) {
//   final tween = Tween(
//     begin: const Offset(0, -1),
//     end: const Offset(0, 0),
//   ).chain(
//     CurveTween(curve: Curves.fastOutSlowIn),
//   );
//   return SlideTransition(
//     position: animation.drive(tween),
//     child: child,
//   );
// };
