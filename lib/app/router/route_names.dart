/// Route names for the application
class RouteNames {
  // Root routes
  static const String splash = '/';
  static const String home = '/home';
  static const String onboarding = '/onboarding';
  
  // Authentication routes
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String resetPassword = '/reset-password';
  
  // Product routes
  static const String products = '/products';
  static const String productDetails = '/products/:id';
  static const String productSearch = '/products/search';
  static const String productCategory = '/products/category/:categoryId';
  
  // Cart routes
  static const String cart = '/cart';
  static const String cartSummary = '/cart/summary';
  
  // Checkout routes
  static const String checkout = '/checkout';
  static const String checkoutShipping = '/checkout/shipping';
  static const String checkoutPayment = '/checkout/payment';
  static const String checkoutReview = '/checkout/review';
  static const String orderConfirmation = '/checkout/confirmation';
  
  // User routes
  static const String profile = '/profile';
  static const String editProfile = '/profile/edit';
  static const String orders = '/orders';
  static const String orderDetails = '/orders/:id';
  static const String wishlist = '/wishlist';
  static const String addresses = '/addresses';
  static const String addAddress = '/addresses/add';
  static const String editAddress = '/addresses/:id/edit';
  
  // Settings routes
  static const String settings = '/settings';
  static const String notifications = '/settings/notifications';
  static const String privacy = '/settings/privacy';
  static const String about = '/settings/about';
  static const String help = '/settings/help';
  
  // Platform routes
  static const String platformSelection = '/platform-selection';
  static const String platformSettings = '/platform-settings';
  
  // Error routes
  static const String error = '/error';
  static const String notFound = '/not-found';
  
  /// Get product details route with ID
  static String getProductDetailsRoute(String productId) {
    return productDetails.replaceAll(':id', productId);
  }
  
  /// Get product category route with category ID
  static String getProductCategoryRoute(String categoryId) {
    return productCategory.replaceAll(':categoryId', categoryId);
  }
  
  /// Get order details route with ID
  static String getOrderDetailsRoute(String orderId) {
    return orderDetails.replaceAll(':id', orderId);
  }
  
  /// Get edit address route with ID
  static String getEditAddressRoute(String addressId) {
    return editAddress.replaceAll(':id', addressId);
  }
  
  /// Check if route requires authentication
  static bool requiresAuth(String route) {
    const authRequiredRoutes = [
      profile,
      editProfile,
      orders,
      wishlist,
      addresses,
      addAddress,
      checkout,
      checkoutShipping,
      checkoutPayment,
      checkoutReview,
    ];
    
    return authRequiredRoutes.any((authRoute) => route.startsWith(authRoute));
  }
  
  /// Check if route is public (no auth required)
  static bool isPublicRoute(String route) {
    const publicRoutes = [
      splash,
      onboarding,
      login,
      register,
      forgotPassword,
      resetPassword,
      home,
      products,
      productSearch,
      error,
      notFound,
    ];
    
    return publicRoutes.any((publicRoute) => 
      route == publicRoute || 
      route.startsWith(productDetails.split(':')[0]) ||
      route.startsWith(productCategory.split(':')[0])
    );
  }
  
  /// Get route title for display
  static String getRouteTitle(String route) {
    switch (route) {
      case splash:
        return 'Splash';
      case home:
        return 'Home';
      case onboarding:
        return 'Welcome';
      case login:
        return 'Login';
      case register:
        return 'Register';
      case forgotPassword:
        return 'Forgot Password';
      case resetPassword:
        return 'Reset Password';
      case products:
        return 'Products';
      case productSearch:
        return 'Search Products';
      case cart:
        return 'Shopping Cart';
      case cartSummary:
        return 'Cart Summary';
      case checkout:
        return 'Checkout';
      case checkoutShipping:
        return 'Shipping Information';
      case checkoutPayment:
        return 'Payment';
      case checkoutReview:
        return 'Review Order';
      case orderConfirmation:
        return 'Order Confirmation';
      case profile:
        return 'Profile';
      case editProfile:
        return 'Edit Profile';
      case orders:
        return 'My Orders';
      case wishlist:
        return 'Wishlist';
      case addresses:
        return 'Addresses';
      case addAddress:
        return 'Add Address';
      case settings:
        return 'Settings';
      case notifications:
        return 'Notifications';
      case privacy:
        return 'Privacy';
      case about:
        return 'About';
      case help:
        return 'Help';
      case platformSelection:
        return 'Select Platform';
      case platformSettings:
        return 'Platform Settings';
      case error:
        return 'Error';
      case notFound:
        return 'Page Not Found';
      default:
        if (route.contains('/products/')) {
          return 'Product Details';
        } else if (route.contains('/orders/')) {
          return 'Order Details';
        } else if (route.contains('/addresses/') && route.contains('/edit')) {
          return 'Edit Address';
        }
        return 'Unknown';
    }
  }
}
