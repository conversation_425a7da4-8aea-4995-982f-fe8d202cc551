import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../core/constants/app_constants.dart';
import '../shared/presentation/pages/splash/splash_page.dart';
import 'app_bloc_observer.dart';
import 'router/app_router.dart';

/// Main application widget
class EcommerceApp extends StatelessWidget {
  const EcommerceApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,
      routerConfig: AppRouter.router,
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF769CDF), // From material-theme.json
        ),
      ),
      darkTheme: ThemeData(
        useMaterial3: true,
        brightness: Brightness.dark,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF769CDF),
          brightness: Brightness.dark,
        ),
      ),
      themeMode: ThemeMode.system,
    );
  }
}

/// App initialization and setup
class AppInitializer {
  /// Initialize the application
  static Future<void> initialize() async {
    WidgetsFlutterBinding.ensureInitialized();
    
    // Set up BLoC observer
    Bloc.observer = AppBlocObserver();
    
    // Initialize dependencies
    // await configureDependencies();
    
    // Initialize storage
    // await _initializeStorage();
    
    // Initialize other services
    // await _initializeServices();
  }
  
  /// Initialize storage services
  static Future<void> _initializeStorage() async {
    // TODO: Initialize Hive, SharedPreferences, etc.
  }
  
  /// Initialize other services
  static Future<void> _initializeServices() async {
    // TODO: Initialize analytics, crash reporting, etc.
  }
}
