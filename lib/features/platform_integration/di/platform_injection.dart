import 'package:get_it/get_it.dart';
import '../domain/usecases/get_products_usecase.dart';
import '../domain/usecases/get_product_usecase.dart';
import '../domain/usecases/get_collections_usecase.dart';
import '../presentation/bloc/platform_bloc.dart';
import '../../../core/platform/platform_factory.dart';

/// Dependency injection setup for platform integration feature
class PlatformInjection {
  static final GetIt _locator = GetIt.instance;

  /// Register platform integration dependencies
  static void registerDependencies() {
    // Register use cases
    _locator.registerLazySingleton<GetProductsUseCase>(
      () => GetProductsUseCase(PlatformFactory.repository),
    );

    _locator.registerLazySingleton<GetProductUseCase>(
      () => GetProductUseCase(PlatformFactory.repository),
    );

    _locator.registerLazySingleton<GetCollectionsUseCase>(
      () => GetCollectionsUseCase(PlatformFactory.repository),
    );

    // Register BLoC
    _locator.registerFactory<PlatformBloc>(
      () => PlatformBloc(
        getProductsUseCase: _locator<GetProductsUseCase>(),
        getProductUseCase: _locator<GetProductUseCase>(),
        getCollectionsUseCase: _locator<GetCollectionsUseCase>(),
      ),
    );
  }

  /// Unregister platform integration dependencies
  static Future<void> unregisterDependencies() async {
    if (_locator.isRegistered<PlatformBloc>()) {
      await _locator.unregister<PlatformBloc>();
    }
    if (_locator.isRegistered<GetProductsUseCase>()) {
      await _locator.unregister<GetProductsUseCase>();
    }
    if (_locator.isRegistered<GetProductUseCase>()) {
      await _locator.unregister<GetProductUseCase>();
    }
    if (_locator.isRegistered<GetCollectionsUseCase>()) {
      await _locator.unregister<GetCollectionsUseCase>();
    }
  }

  /// Re-register dependencies (useful when switching platforms)
  static Future<void> reregisterDependencies() async {
    await unregisterDependencies();
    registerDependencies();
  }
}
