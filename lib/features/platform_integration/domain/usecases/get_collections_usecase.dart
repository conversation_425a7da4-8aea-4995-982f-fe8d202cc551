import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/domain/usecases/usecase.dart';
import '../entities/platform_collection.dart';
import '../repositories/platform_repository.dart';

/// Use case for getting collections from the platform
class GetCollectionsUseCase implements UseCase<List<PlatformCollection>, GetCollectionsParams> {
  const GetCollectionsUseCase(this._repository);

  final PlatformRepository _repository;

  @override
  Future<Either<Failure, List<PlatformCollection>>> call(GetCollectionsParams params) async {
    return await _repository.getCollections(
      page: params.page,
      limit: params.limit,
    );
  }
}

/// Parameters for getting collections
class GetCollectionsParams {
  const GetCollectionsParams({
    this.page = 1,
    this.limit = 20,
  });

  final int page;
  final int limit;
}
