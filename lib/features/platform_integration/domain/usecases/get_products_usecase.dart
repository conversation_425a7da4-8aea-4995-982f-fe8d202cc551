import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/domain/usecases/usecase.dart';
import '../entities/platform_product.dart';
import '../repositories/platform_repository.dart';

/// Use case for getting products from the platform
class GetProductsUseCase implements UseCase<List<PlatformProduct>, GetProductsParams> {
  const GetProductsUseCase(this._repository);

  final PlatformRepository _repository;

  @override
  Future<Either<Failure, List<PlatformProduct>>> call(GetProductsParams params) async {
    return await _repository.getProducts(
      page: params.page,
      limit: params.limit,
      collectionId: params.collectionId,
      query: params.query,
      filters: params.filters,
    );
  }
}

/// Parameters for getting products
class GetProductsParams {
  const GetProductsParams({
    this.page = 1,
    this.limit = 20,
    this.collectionId,
    this.query,
    this.filters,
  });

  final int page;
  final int limit;
  final String? collectionId;
  final String? query;
  final Map<String, dynamic>? filters;
}
