import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../../../../shared/domain/usecases/usecase.dart';
import '../entities/platform_product.dart';
import '../repositories/platform_repository.dart';

/// Use case for getting a single product from the platform
class GetProductUseCase implements UseCase<PlatformProduct, GetProductParams> {
  const GetProductUseCase(this._repository);

  final PlatformRepository _repository;

  @override
  Future<Either<Failure, PlatformProduct>> call(GetProductParams params) async {
    return await _repository.getProduct(params.productId);
  }
}

/// Parameters for getting a single product
class GetProductParams {
  const GetProductParams({
    required this.productId,
  });

  final String productId;
}
