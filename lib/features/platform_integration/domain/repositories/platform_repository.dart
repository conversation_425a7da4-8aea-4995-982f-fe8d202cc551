import 'package:dartz/dartz.dart';
import '../../../../core/errors/failures.dart';
import '../entities/platform_product.dart';
import '../entities/platform_collection.dart';

/// Abstract repository interface for platform operations
abstract class PlatformRepository {
  /// Get products with pagination
  Future<Either<Failure, List<PlatformProduct>>> getProducts({
    int page = 1,
    int limit = 20,
    String? collectionId,
    String? query,
    Map<String, dynamic>? filters,
  });

  /// Get a single product by ID
  Future<Either<Failure, PlatformProduct>> getProduct(String productId);

  /// Get collections/categories
  Future<Either<Failure, List<PlatformCollection>>> getCollections({
    int page = 1,
    int limit = 20,
  });

  /// Get a single collection by ID
  Future<Either<Failure, PlatformCollection>> getCollection(String collectionId);

  /// Search products
  Future<Either<Failure, List<PlatformProduct>>> searchProducts({
    required String query,
    int page = 1,
    int limit = 20,
    Map<String, dynamic>? filters,
  });

  /// Get product recommendations
  Future<Either<Failure, List<PlatformProduct>>> getRecommendedProducts({
    String? productId,
    int limit = 10,
  });

  /// Check product availability
  Future<Either<Failure, bool>> checkProductAvailability(
    String productId, {
    String? variantId,
    int quantity = 1,
  });

  /// Get platform-specific configuration
  Future<Either<Failure, Map<String, dynamic>>> getPlatformConfig();

  /// Test platform connection
  Future<Either<Failure, bool>> testConnection();
}

/// Query parameters for product filtering
class ProductQueryParams {
  const ProductQueryParams({
    this.page = 1,
    this.limit = 20,
    this.collectionId,
    this.query,
    this.sortBy,
    this.sortOrder = SortOrder.ascending,
    this.priceMin,
    this.priceMax,
    this.vendor,
    this.productType,
    this.tags = const [],
    this.available,
  });

  final int page;
  final int limit;
  final String? collectionId;
  final String? query;
  final String? sortBy;
  final SortOrder sortOrder;
  final double? priceMin;
  final double? priceMax;
  final String? vendor;
  final String? productType;
  final List<String> tags;
  final bool? available;

  /// Convert to map for API calls
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{
      'page': page,
      'limit': limit,
    };

    if (collectionId != null) map['collection_id'] = collectionId;
    if (query != null) map['query'] = query;
    if (sortBy != null) map['sort_by'] = sortBy;
    if (sortOrder == SortOrder.descending) map['sort_order'] = 'desc';
    if (priceMin != null) map['price_min'] = priceMin;
    if (priceMax != null) map['price_max'] = priceMax;
    if (vendor != null) map['vendor'] = vendor;
    if (productType != null) map['product_type'] = productType;
    if (tags.isNotEmpty) map['tags'] = tags.join(',');
    if (available != null) map['available'] = available;

    return map;
  }
}

/// Sort order enumeration
enum SortOrder {
  ascending,
  descending,
}

/// Product sort options
enum ProductSortBy {
  title('title'),
  price('price'),
  createdAt('created_at'),
  updatedAt('updated_at'),
  bestSelling('best_selling'),
  manual('manual');

  const ProductSortBy(this.value);
  final String value;
}
