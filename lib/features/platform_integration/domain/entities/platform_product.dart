import 'package:equatable/equatable.dart';

/// Platform-agnostic product entity
class PlatformProduct extends Equatable {
  const PlatformProduct({
    required this.id,
    required this.title,
    required this.description,
    required this.price,
    required this.currency,
    required this.images,
    required this.variants,
    required this.isAvailable,
    required this.createdAt,
    required this.updatedAt,
    this.compareAtPrice,
    this.vendor,
    this.productType,
    this.tags = const [],
    this.handle,
    this.seo,
  });

  final String id;
  final String title;
  final String description;
  final double price;
  final String currency;
  final List<PlatformProductImage> images;
  final List<PlatformProductVariant> variants;
  final bool isAvailable;
  final DateTime createdAt;
  final DateTime updatedAt;
  final double? compareAtPrice;
  final String? vendor;
  final String? productType;
  final List<String> tags;
  final String? handle;
  final PlatformProductSEO? seo;

  /// Get the main product image
  PlatformProductImage? get mainImage => 
      images.isNotEmpty ? images.first : null;

  /// Check if product is on sale
  bool get isOnSale => 
      compareAtPrice != null && compareAtPrice! > price;

  /// Get discount percentage
  double get discountPercentage {
    if (!isOnSale || compareAtPrice == null) return 0.0;
    return ((compareAtPrice! - price) / compareAtPrice!) * 100;
  }

  /// Get price range for variants
  PriceRange get priceRange {
    if (variants.isEmpty) {
      return PriceRange(min: price, max: price);
    }
    
    final prices = variants.map((v) => v.price).toList();
    return PriceRange(
      min: prices.reduce((a, b) => a < b ? a : b),
      max: prices.reduce((a, b) => a > b ? a : b),
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        price,
        currency,
        images,
        variants,
        isAvailable,
        createdAt,
        updatedAt,
        compareAtPrice,
        vendor,
        productType,
        tags,
        handle,
        seo,
      ];
}

/// Product image entity
class PlatformProductImage extends Equatable {
  const PlatformProductImage({
    required this.id,
    required this.url,
    required this.altText,
    this.width,
    this.height,
    this.position = 0,
  });

  final String id;
  final String url;
  final String altText;
  final int? width;
  final int? height;
  final int position;

  @override
  List<Object?> get props => [id, url, altText, width, height, position];
}

/// Product variant entity
class PlatformProductVariant extends Equatable {
  const PlatformProductVariant({
    required this.id,
    required this.title,
    required this.price,
    required this.isAvailable,
    required this.inventoryQuantity,
    this.compareAtPrice,
    this.sku,
    this.barcode,
    this.weight,
    this.options = const {},
  });

  final String id;
  final String title;
  final double price;
  final bool isAvailable;
  final int inventoryQuantity;
  final double? compareAtPrice;
  final String? sku;
  final String? barcode;
  final double? weight;
  final Map<String, String> options;

  /// Check if variant is on sale
  bool get isOnSale => 
      compareAtPrice != null && compareAtPrice! > price;

  @override
  List<Object?> get props => [
        id,
        title,
        price,
        isAvailable,
        inventoryQuantity,
        compareAtPrice,
        sku,
        barcode,
        weight,
        options,
      ];
}

/// Product SEO entity
class PlatformProductSEO extends Equatable {
  const PlatformProductSEO({
    this.title,
    this.description,
  });

  final String? title;
  final String? description;

  @override
  List<Object?> get props => [title, description];
}

/// Price range helper class
class PriceRange extends Equatable {
  const PriceRange({
    required this.min,
    required this.max,
  });

  final double min;
  final double max;

  /// Check if price range is single price
  bool get isSinglePrice => min == max;

  @override
  List<Object> get props => [min, max];
}
