import 'package:equatable/equatable.dart';

/// Platform-agnostic collection/category entity
class PlatformCollection extends Equatable {
  const PlatformCollection({
    required this.id,
    required this.title,
    required this.description,
    required this.handle,
    required this.createdAt,
    required this.updatedAt,
    this.image,
    this.productCount = 0,
    this.sortOrder,
    this.rules = const [],
    this.seo,
  });

  final String id;
  final String title;
  final String description;
  final String handle;
  final DateTime createdAt;
  final DateTime updatedAt;
  final PlatformCollectionImage? image;
  final int productCount;
  final String? sortOrder;
  final List<PlatformCollectionRule> rules;
  final PlatformCollectionSEO? seo;

  /// Check if collection is automated (has rules)
  bool get isAutomated => rules.isNotEmpty;

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        handle,
        createdAt,
        updatedAt,
        image,
        productCount,
        sortOrder,
        rules,
        seo,
      ];
}

/// Collection image entity
class PlatformCollectionImage extends Equatable {
  const PlatformCollectionImage({
    required this.url,
    required this.altText,
    this.width,
    this.height,
  });

  final String url;
  final String altText;
  final int? width;
  final int? height;

  @override
  List<Object?> get props => [url, altText, width, height];
}

/// Collection rule entity for automated collections
class PlatformCollectionRule extends Equatable {
  const PlatformCollectionRule({
    required this.column,
    required this.relation,
    required this.condition,
  });

  final String column;
  final String relation;
  final String condition;

  @override
  List<Object> get props => [column, relation, condition];
}

/// Collection SEO entity
class PlatformCollectionSEO extends Equatable {
  const PlatformCollectionSEO({
    this.title,
    this.description,
  });

  final String? title;
  final String? description;

  @override
  List<Object?> get props => [title, description];
}
