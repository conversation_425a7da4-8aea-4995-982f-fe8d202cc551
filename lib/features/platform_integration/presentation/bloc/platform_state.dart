part of 'platform_bloc.dart';

/// Status of platform integration
enum PlatformStatus {
  initial,
  loading,
  ready,
  failure,
}

/// State for platform integration
class PlatformState extends Equatable {
  const PlatformState({
    this.status = PlatformStatus.initial,
    this.currentPlatform,
    this.config,
    this.products = const [],
    this.collections = const [],
    this.selectedProduct,
    this.isLoadingProducts = false,
    this.isLoadingProduct = false,
    this.isLoadingCollections = false,
    this.isTestingConnection = false,
    this.hasMoreProducts = false,
    this.hasMoreCollections = false,
    this.errorMessage,
    this.connectionStatus,
  });

  final PlatformStatus status;
  final PlatformType? currentPlatform;
  final PlatformConfig? config;
  final List<PlatformProduct> products;
  final List<PlatformCollection> collections;
  final PlatformProduct? selectedProduct;
  final bool isLoadingProducts;
  final bool isLoadingProduct;
  final bool isLoadingCollections;
  final bool isTestingConnection;
  final bool hasMoreProducts;
  final bool hasMoreCollections;
  final String? errorMessage;
  final String? connectionStatus;

  /// Check if platform is ready for operations
  bool get isReady => status == PlatformStatus.ready;

  /// Check if any loading operation is in progress
  bool get isLoading => 
      status == PlatformStatus.loading ||
      isLoadingProducts ||
      isLoadingProduct ||
      isLoadingCollections ||
      isTestingConnection;

  /// Check if there's an error
  bool get hasError => status == PlatformStatus.failure || errorMessage != null;

  /// Get platform display name
  String get platformDisplayName => currentPlatform?.displayName ?? 'Unknown';

  /// Copy state with updated values
  PlatformState copyWith({
    PlatformStatus? status,
    PlatformType? currentPlatform,
    PlatformConfig? config,
    List<PlatformProduct>? products,
    List<PlatformCollection>? collections,
    PlatformProduct? selectedProduct,
    bool? isLoadingProducts,
    bool? isLoadingProduct,
    bool? isLoadingCollections,
    bool? isTestingConnection,
    bool? hasMoreProducts,
    bool? hasMoreCollections,
    String? errorMessage,
    String? connectionStatus,
  }) {
    return PlatformState(
      status: status ?? this.status,
      currentPlatform: currentPlatform ?? this.currentPlatform,
      config: config ?? this.config,
      products: products ?? this.products,
      collections: collections ?? this.collections,
      selectedProduct: selectedProduct ?? this.selectedProduct,
      isLoadingProducts: isLoadingProducts ?? this.isLoadingProducts,
      isLoadingProduct: isLoadingProduct ?? this.isLoadingProduct,
      isLoadingCollections: isLoadingCollections ?? this.isLoadingCollections,
      isTestingConnection: isTestingConnection ?? this.isTestingConnection,
      hasMoreProducts: hasMoreProducts ?? this.hasMoreProducts,
      hasMoreCollections: hasMoreCollections ?? this.hasMoreCollections,
      errorMessage: errorMessage ?? this.errorMessage,
      connectionStatus: connectionStatus ?? this.connectionStatus,
    );
  }

  @override
  List<Object?> get props => [
        status,
        currentPlatform,
        config,
        products,
        collections,
        selectedProduct,
        isLoadingProducts,
        isLoadingProduct,
        isLoadingCollections,
        isTestingConnection,
        hasMoreProducts,
        hasMoreCollections,
        errorMessage,
        connectionStatus,
      ];
}
