import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/platform/platform_config.dart';
import '../../../../core/platform/platform_factory.dart';
import '../../../../core/platform/platform_type.dart';
import '../../domain/entities/platform_product.dart';
import '../../domain/entities/platform_collection.dart';
import '../../domain/usecases/get_products_usecase.dart';
import '../../domain/usecases/get_product_usecase.dart';
import '../../domain/usecases/get_collections_usecase.dart';

part 'platform_event.dart';
part 'platform_state.dart';

/// BLoC for managing platform integration
class PlatformBloc extends Bloc<PlatformEvent, PlatformState> {
  PlatformBloc({
    required this.getProductsUseCase,
    required this.getProductUseCase,
    required this.getCollectionsUseCase,
  }) : super(const PlatformState()) {
    on<PlatformInitialized>(_onPlatformInitialized);
    on<PlatformSwitched>(_onPlatformSwitched);
    on<ProductsRequested>(_onProductsRequested);
    on<ProductRequested>(_onProductRequested);
    on<CollectionsRequested>(_onCollectionsRequested);
    on<ProductsSearched>(_onProductsSearched);
    on<ConnectionTested>(_onConnectionTested);
  }

  final GetProductsUseCase getProductsUseCase;
  final GetProductUseCase getProductUseCase;
  final GetCollectionsUseCase getCollectionsUseCase;

  /// Initialize platform with configuration
  Future<void> _onPlatformInitialized(
    PlatformInitialized event,
    Emitter<PlatformState> emit,
  ) async {
    emit(state.copyWith(status: PlatformStatus.loading));

    try {
      await PlatformFactory.registerPlatformDependencies(event.config);
      
      // Test connection
      final repository = PlatformFactory.repository;
      final connectionResult = await repository.testConnection();
      
      connectionResult.fold(
        (failure) => emit(state.copyWith(
          status: PlatformStatus.failure,
          errorMessage: failure.message,
        )),
        (isConnected) {
          if (isConnected) {
            emit(state.copyWith(
              status: PlatformStatus.ready,
              currentPlatform: event.config.platformType,
              config: event.config,
            ));
          } else {
            emit(state.copyWith(
              status: PlatformStatus.failure,
              errorMessage: 'Failed to connect to platform',
            ));
          }
        },
      );
    } catch (e) {
      emit(state.copyWith(
        status: PlatformStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Switch to a different platform
  Future<void> _onPlatformSwitched(
    PlatformSwitched event,
    Emitter<PlatformState> emit,
  ) async {
    emit(state.copyWith(status: PlatformStatus.loading));

    try {
      await PlatformFactory.switchPlatform(event.config);
      
      // Test new connection
      final repository = PlatformFactory.repository;
      final connectionResult = await repository.testConnection();
      
      connectionResult.fold(
        (failure) => emit(state.copyWith(
          status: PlatformStatus.failure,
          errorMessage: failure.message,
        )),
        (isConnected) {
          if (isConnected) {
            emit(state.copyWith(
              status: PlatformStatus.ready,
              currentPlatform: event.config.platformType,
              config: event.config,
              // Clear previous data when switching platforms
              products: [],
              collections: [],
              selectedProduct: null,
            ));
          } else {
            emit(state.copyWith(
              status: PlatformStatus.failure,
              errorMessage: 'Failed to connect to new platform',
            ));
          }
        },
      );
    } catch (e) {
      emit(state.copyWith(
        status: PlatformStatus.failure,
        errorMessage: e.toString(),
      ));
    }
  }

  /// Load products
  Future<void> _onProductsRequested(
    ProductsRequested event,
    Emitter<PlatformState> emit,
  ) async {
    if (state.status != PlatformStatus.ready) {
      emit(state.copyWith(
        status: PlatformStatus.failure,
        errorMessage: 'Platform not ready',
      ));
      return;
    }

    emit(state.copyWith(isLoadingProducts: true));

    final result = await getProductsUseCase(GetProductsParams(
      page: event.page,
      limit: event.limit,
      collectionId: event.collectionId,
      query: event.query,
      filters: event.filters,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        isLoadingProducts: false,
        errorMessage: failure.message,
      )),
      (products) {
        final updatedProducts = event.page == 1 
            ? products 
            : [...state.products, ...products];
        
        emit(state.copyWith(
          isLoadingProducts: false,
          products: updatedProducts,
          hasMoreProducts: products.length == event.limit,
        ));
      },
    );
  }

  /// Load single product
  Future<void> _onProductRequested(
    ProductRequested event,
    Emitter<PlatformState> emit,
  ) async {
    if (state.status != PlatformStatus.ready) {
      emit(state.copyWith(
        status: PlatformStatus.failure,
        errorMessage: 'Platform not ready',
      ));
      return;
    }

    emit(state.copyWith(isLoadingProduct: true));

    final result = await getProductUseCase(GetProductParams(
      productId: event.productId,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        isLoadingProduct: false,
        errorMessage: failure.message,
      )),
      (product) => emit(state.copyWith(
        isLoadingProduct: false,
        selectedProduct: product,
      )),
    );
  }

  /// Load collections
  Future<void> _onCollectionsRequested(
    CollectionsRequested event,
    Emitter<PlatformState> emit,
  ) async {
    if (state.status != PlatformStatus.ready) {
      emit(state.copyWith(
        status: PlatformStatus.failure,
        errorMessage: 'Platform not ready',
      ));
      return;
    }

    emit(state.copyWith(isLoadingCollections: true));

    final result = await getCollectionsUseCase(GetCollectionsParams(
      page: event.page,
      limit: event.limit,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        isLoadingCollections: false,
        errorMessage: failure.message,
      )),
      (collections) {
        final updatedCollections = event.page == 1 
            ? collections 
            : [...state.collections, ...collections];
        
        emit(state.copyWith(
          isLoadingCollections: false,
          collections: updatedCollections,
          hasMoreCollections: collections.length == event.limit,
        ));
      },
    );
  }

  /// Search products
  Future<void> _onProductsSearched(
    ProductsSearched event,
    Emitter<PlatformState> emit,
  ) async {
    // Use the same logic as ProductsRequested but with search query
    add(ProductsRequested(
      page: 1,
      limit: event.limit,
      query: event.query,
      filters: event.filters,
    ));
  }

  /// Test platform connection
  Future<void> _onConnectionTested(
    ConnectionTested event,
    Emitter<PlatformState> emit,
  ) async {
    if (!PlatformFactory.isPlatformRegistered) {
      emit(state.copyWith(
        errorMessage: 'No platform configured',
      ));
      return;
    }

    emit(state.copyWith(isTestingConnection: true));

    final repository = PlatformFactory.repository;
    final result = await repository.testConnection();

    result.fold(
      (failure) => emit(state.copyWith(
        isTestingConnection: false,
        errorMessage: failure.message,
      )),
      (isConnected) => emit(state.copyWith(
        isTestingConnection: false,
        connectionStatus: isConnected ? 'Connected' : 'Disconnected',
      )),
    );
  }
}
