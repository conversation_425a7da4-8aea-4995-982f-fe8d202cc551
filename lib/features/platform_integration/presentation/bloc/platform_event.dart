part of 'platform_bloc.dart';

/// Base class for platform events
abstract class Platform<PERSON>vent extends Equatable {
  const PlatformEvent();

  @override
  List<Object?> get props => [];
}

/// Event to initialize platform with configuration
class PlatformInitialized extends PlatformEvent {
  const PlatformInitialized({
    required this.config,
  });

  final PlatformConfig config;

  @override
  List<Object> get props => [config];
}

/// Event to switch to a different platform
class PlatformSwitched extends PlatformEvent {
  const PlatformSwitched({
    required this.config,
  });

  final PlatformConfig config;

  @override
  List<Object> get props => [config];
}

/// Event to request products
class ProductsRequested extends PlatformEvent {
  const ProductsRequested({
    this.page = 1,
    this.limit = 20,
    this.collectionId,
    this.query,
    this.filters,
  });

  final int page;
  final int limit;
  final String? collectionId;
  final String? query;
  final Map<String, dynamic>? filters;

  @override
  List<Object?> get props => [page, limit, collectionId, query, filters];
}

/// Event to request a single product
class ProductRequested extends PlatformEvent {
  const ProductRequested({
    required this.productId,
  });

  final String productId;

  @override
  List<Object> get props => [productId];
}

/// Event to request collections
class CollectionsRequested extends PlatformEvent {
  const CollectionsRequested({
    this.page = 1,
    this.limit = 20,
  });

  final int page;
  final int limit;

  @override
  List<Object> get props => [page, limit];
}

/// Event to search products
class ProductsSearched extends PlatformEvent {
  const ProductsSearched({
    required this.query,
    this.limit = 20,
    this.filters,
  });

  final String query;
  final int limit;
  final Map<String, dynamic>? filters;

  @override
  List<Object?> get props => [query, limit, filters];
}

/// Event to test platform connection
class ConnectionTested extends PlatformEvent {
  const ConnectionTested();
}

/// Event to clear platform data
class PlatformDataCleared extends PlatformEvent {
  const PlatformDataCleared();
}

/// Event to refresh current data
class PlatformDataRefreshed extends PlatformEvent {
  const PlatformDataRefreshed();
}
