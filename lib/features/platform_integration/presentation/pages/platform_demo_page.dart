import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/platform/platform_config.dart';
import '../../domain/entities/platform_product.dart';
import '../bloc/platform_bloc.dart';

/// Demo page to showcase platform integration functionality
class PlatformDemoPage extends StatelessWidget {
  const PlatformDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Platform Integration Demo'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: BlocProvider(
        create: (context) => PlatformBloc(
          getProductsUseCase: context.read(),
          getProductUseCase: context.read(),
          getCollectionsUseCase: context.read(),
        ),
        child: const PlatformDemoView(),
      ),
    );
  }
}

class PlatformDemoView extends StatefulWidget {
  const PlatformDemoView({super.key});

  @override
  State<PlatformDemoView> createState() => _PlatformDemoViewState();
}

class _PlatformDemoViewState extends State<PlatformDemoView> {
  @override
  void initState() {
    super.initState();
    // Initialize with mock platform for demo
    context.read<PlatformBloc>().add(
          PlatformInitialized(config: PlatformConfig.mock()),
        );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PlatformBloc, PlatformState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildPlatformStatus(context, state),
              const SizedBox(height: 20),
              _buildPlatformControls(context, state),
              const SizedBox(height: 20),
              _buildProductsList(context, state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPlatformStatus(BuildContext context, PlatformState state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Platform Status',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  _getStatusIcon(state.status),
                  color: _getStatusColor(state.status),
                ),
                const SizedBox(width: 8),
                Text('Status: ${state.status.name}'),
              ],
            ),
            if (state.currentPlatform != null) ...[
              const SizedBox(height: 4),
              Text('Platform: ${state.platformDisplayName}'),
            ],
            if (state.errorMessage != null) ...[
              const SizedBox(height: 4),
              Text(
                'Error: ${state.errorMessage}',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPlatformControls(BuildContext context, PlatformState state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Platform Controls',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              children: [
                ElevatedButton(
                  onPressed: state.isReady
                      ? () => context.read<PlatformBloc>().add(
                            const ProductsRequested(),
                          )
                      : null,
                  child: const Text('Load Products'),
                ),
                ElevatedButton(
                  onPressed: state.isReady
                      ? () => context.read<PlatformBloc>().add(
                            const CollectionsRequested(),
                          )
                      : null,
                  child: const Text('Load Collections'),
                ),
                ElevatedButton(
                  onPressed: () => context.read<PlatformBloc>().add(
                        const ConnectionTested(),
                      ),
                  child: const Text('Test Connection'),
                ),
                ElevatedButton(
                  onPressed: () => _showPlatformSwitcher(context),
                  child: const Text('Switch Platform'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductsList(BuildContext context, PlatformState state) {
    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'Products (${state.products.length})',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  if (state.isLoadingProducts) ...[
                    const SizedBox(width: 8),
                    const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: state.products.isEmpty
                    ? const Center(
                        child: Text('No products loaded. Click "Load Products" to fetch data.'),
                      )
                    : ListView.builder(
                        itemCount: state.products.length,
                        itemBuilder: (context, index) {
                          final product = state.products[index];
                          return ListTile(
                            leading: product.mainImage != null
                                ? CircleAvatar(
                                    backgroundImage: NetworkImage(product.mainImage!.url),
                                  )
                                : const CircleAvatar(
                                    child: Icon(Icons.shopping_bag),
                                  ),
                            title: Text(product.title),
                            subtitle: Text('\$${product.price.toStringAsFixed(2)}'),
                            trailing: product.isOnSale
                                ? Chip(
                                    label: Text('${product.discountPercentage.toStringAsFixed(0)}% OFF'),
                                    backgroundColor: Theme.of(context).colorScheme.secondary,
                                  )
                                : null,
                            onTap: () => _showProductDetails(context, product),
                          );
                        },
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getStatusIcon(PlatformStatus status) {
    switch (status) {
      case PlatformStatus.initial:
        return Icons.circle_outlined;
      case PlatformStatus.loading:
        return Icons.refresh;
      case PlatformStatus.ready:
        return Icons.check_circle;
      case PlatformStatus.failure:
        return Icons.error;
    }
  }

  Color _getStatusColor(PlatformStatus status) {
    switch (status) {
      case PlatformStatus.initial:
        return Colors.grey;
      case PlatformStatus.loading:
        return Colors.orange;
      case PlatformStatus.ready:
        return Colors.green;
      case PlatformStatus.failure:
        return Colors.red;
    }
  }

  void _showPlatformSwitcher(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Switch Platform'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Mock Platform'),
              subtitle: const Text('For development and testing'),
              onTap: () {
                context.read<PlatformBloc>().add(
                      PlatformSwitched(config: PlatformConfig.mock()),
                    );
                Navigator.of(dialogContext).pop();
              },
            ),
            ListTile(
              title: const Text('Shopify'),
              subtitle: const Text('Connect to Shopify store'),
              onTap: () {
                // In a real app, you'd show a form to enter Shopify credentials
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Shopify integration requires configuration'),
                  ),
                );
                Navigator.of(dialogContext).pop();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showProductDetails(BuildContext context, PlatformProduct product) {
    showDialog<void>(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Text(product.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Price: \$${product.price.toStringAsFixed(2)}'),
            if (product.compareAtPrice != null)
              Text('Compare at: \$${product.compareAtPrice!.toStringAsFixed(2)}'),
            const SizedBox(height: 8),
            Text('Vendor: ${product.vendor ?? 'Unknown'}'),
            Text('Type: ${product.productType ?? 'Unknown'}'),
            Text('Available: ${product.isAvailable ? 'Yes' : 'No'}'),
            if (product.tags.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text('Tags: ${product.tags.join(', ')}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
