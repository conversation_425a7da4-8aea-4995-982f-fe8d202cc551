import 'package:dartz/dartz.dart';
import '../../../../core/errors/exceptions.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/platform/platform_config.dart';
import '../../domain/entities/platform_product.dart';
import '../../domain/entities/platform_collection.dart';
import '../../domain/repositories/platform_repository.dart';
import '../datasources/platform_remote_datasource.dart';

/// Implementation of platform repository
class PlatformRepositoryImpl implements PlatformRepository {
  const PlatformRepositoryImpl({
    required this.remoteDataSource,
    required this.config,
  });

  final PlatformRemoteDataSource remoteDataSource;
  final PlatformConfig config;

  @override
  Future<Either<Failure, List<PlatformProduct>>> getProducts({
    int page = 1,
    int limit = 20,
    String? collectionId,
    String? query,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final products = await remoteDataSource.getProducts(
        page: page,
        limit: limit,
        collectionId: collectionId,
        query: query,
        filters: filters,
      );
      return Right(products.map((model) => model.toEntity()).toList());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, PlatformProduct>> getProduct(String productId) async {
    try {
      final product = await remoteDataSource.getProduct(productId);
      return Right(product.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<PlatformCollection>>> getCollections({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final collections = await remoteDataSource.getCollections(
        page: page,
        limit: limit,
      );
      return Right(collections.map((model) => model.toEntity()).toList());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, PlatformCollection>> getCollection(String collectionId) async {
    try {
      final collection = await remoteDataSource.getCollection(collectionId);
      return Right(collection.toEntity());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<PlatformProduct>>> searchProducts({
    required String query,
    int page = 1,
    int limit = 20,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final products = await remoteDataSource.searchProducts(
        query: query,
        page: page,
        limit: limit,
        filters: filters,
      );
      return Right(products.map((model) => model.toEntity()).toList());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<PlatformProduct>>> getRecommendedProducts({
    String? productId,
    int limit = 10,
  }) async {
    try {
      final products = await remoteDataSource.getRecommendedProducts(
        productId: productId,
        limit: limit,
      );
      return Right(products.map((model) => model.toEntity()).toList());
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> checkProductAvailability(
    String productId, {
    String? variantId,
    int quantity = 1,
  }) async {
    try {
      final isAvailable = await remoteDataSource.checkProductAvailability(
        productId,
        variantId: variantId,
        quantity: quantity,
      );
      return Right(isAvailable);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getPlatformConfig() async {
    try {
      final configData = await remoteDataSource.getPlatformConfig();
      return Right(configData);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }

  @override
  Future<Either<Failure, bool>> testConnection() async {
    try {
      final isConnected = await remoteDataSource.testConnection();
      return Right(isConnected);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message));
    } on CacheException catch (e) {
      return Left(CacheFailure(message: e.message));
    } catch (e) {
      return Left(UnknownFailure(message: e.toString()));
    }
  }
}
