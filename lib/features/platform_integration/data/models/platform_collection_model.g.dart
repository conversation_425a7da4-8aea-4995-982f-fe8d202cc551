// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'platform_collection_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PlatformCollectionModel _$PlatformCollectionModelFromJson(
        Map<String, dynamic> json) =>
    PlatformCollectionModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      handle: json['handle'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      image: json['image'] == null
          ? null
          : PlatformCollectionImageModel.fromJson(
              json['image'] as Map<String, dynamic>),
      productCount: (json['productCount'] as num?)?.toInt() ?? 0,
      sortOrder: json['sortOrder'] as String?,
      rules: (json['rules'] as List<dynamic>?)
              ?.map((e) => PlatformCollectionRuleModel.fromJson(
                  e as Map<String, dynamic>))
              .toList() ??
          const [],
      seo: json['seo'] == null
          ? null
          : PlatformCollectionSEOModel.fromJson(
              json['seo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PlatformCollectionModelToJson(
        PlatformCollectionModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'handle': instance.handle,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'image': instance.image,
      'productCount': instance.productCount,
      'sortOrder': instance.sortOrder,
      'rules': instance.rules,
      'seo': instance.seo,
    };

PlatformCollectionImageModel _$PlatformCollectionImageModelFromJson(
        Map<String, dynamic> json) =>
    PlatformCollectionImageModel(
      url: json['url'] as String,
      altText: json['altText'] as String,
      width: (json['width'] as num?)?.toInt(),
      height: (json['height'] as num?)?.toInt(),
    );

Map<String, dynamic> _$PlatformCollectionImageModelToJson(
        PlatformCollectionImageModel instance) =>
    <String, dynamic>{
      'url': instance.url,
      'altText': instance.altText,
      'width': instance.width,
      'height': instance.height,
    };

PlatformCollectionRuleModel _$PlatformCollectionRuleModelFromJson(
        Map<String, dynamic> json) =>
    PlatformCollectionRuleModel(
      column: json['column'] as String,
      relation: json['relation'] as String,
      condition: json['condition'] as String,
    );

Map<String, dynamic> _$PlatformCollectionRuleModelToJson(
        PlatformCollectionRuleModel instance) =>
    <String, dynamic>{
      'column': instance.column,
      'relation': instance.relation,
      'condition': instance.condition,
    };

PlatformCollectionSEOModel _$PlatformCollectionSEOModelFromJson(
        Map<String, dynamic> json) =>
    PlatformCollectionSEOModel(
      title: json['title'] as String?,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$PlatformCollectionSEOModelToJson(
        PlatformCollectionSEOModel instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
    };
