import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/platform_product.dart';

part 'platform_product_model.g.dart';

/// Data model for platform product
@JsonSerializable()
class PlatformProductModel extends PlatformProduct {
  const PlatformProductModel({
    required super.id,
    required super.title,
    required super.description,
    required super.price,
    required super.currency,
    required super.images,
    required super.variants,
    required super.isAvailable,
    required super.createdAt,
    required super.updatedAt,
    super.compareAtPrice,
    super.vendor,
    super.productType,
    super.tags,
    super.handle,
    super.seo,
  });

  /// Create from JSON
  factory PlatformProductModel.fromJson(Map<String, dynamic> json) =>
      _$PlatformProductModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$PlatformProductModelToJson(this);

  /// Create from domain entity
  factory PlatformProductModel.fromEntity(PlatformProduct product) {
    return PlatformProductModel(
      id: product.id,
      title: product.title,
      description: product.description,
      price: product.price,
      currency: product.currency,
      images: product.images.map((img) => 
          PlatformProductImageModel.fromEntity(img)).toList(),
      variants: product.variants.map((variant) => 
          PlatformProductVariantModel.fromEntity(variant)).toList(),
      isAvailable: product.isAvailable,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      compareAtPrice: product.compareAtPrice,
      vendor: product.vendor,
      productType: product.productType,
      tags: product.tags,
      handle: product.handle,
      seo: product.seo != null ? 
          PlatformProductSEOModel.fromEntity(product.seo!) : null,
    );
  }

  /// Convert to domain entity
  PlatformProduct toEntity() {
    return PlatformProduct(
      id: id,
      title: title,
      description: description,
      price: price,
      currency: currency,
      images: images,
      variants: variants,
      isAvailable: isAvailable,
      createdAt: createdAt,
      updatedAt: updatedAt,
      compareAtPrice: compareAtPrice,
      vendor: vendor,
      productType: productType,
      tags: tags,
      handle: handle,
      seo: seo,
    );
  }
}

/// Data model for platform product image
@JsonSerializable()
class PlatformProductImageModel extends PlatformProductImage {
  const PlatformProductImageModel({
    required super.id,
    required super.url,
    required super.altText,
    super.width,
    super.height,
    super.position,
  });

  factory PlatformProductImageModel.fromJson(Map<String, dynamic> json) =>
      _$PlatformProductImageModelFromJson(json);

  Map<String, dynamic> toJson() => _$PlatformProductImageModelToJson(this);

  factory PlatformProductImageModel.fromEntity(PlatformProductImage image) {
    return PlatformProductImageModel(
      id: image.id,
      url: image.url,
      altText: image.altText,
      width: image.width,
      height: image.height,
      position: image.position,
    );
  }
}

/// Data model for platform product variant
@JsonSerializable()
class PlatformProductVariantModel extends PlatformProductVariant {
  const PlatformProductVariantModel({
    required super.id,
    required super.title,
    required super.price,
    required super.isAvailable,
    required super.inventoryQuantity,
    super.compareAtPrice,
    super.sku,
    super.barcode,
    super.weight,
    super.options,
  });

  factory PlatformProductVariantModel.fromJson(Map<String, dynamic> json) =>
      _$PlatformProductVariantModelFromJson(json);

  Map<String, dynamic> toJson() => _$PlatformProductVariantModelToJson(this);

  factory PlatformProductVariantModel.fromEntity(PlatformProductVariant variant) {
    return PlatformProductVariantModel(
      id: variant.id,
      title: variant.title,
      price: variant.price,
      isAvailable: variant.isAvailable,
      inventoryQuantity: variant.inventoryQuantity,
      compareAtPrice: variant.compareAtPrice,
      sku: variant.sku,
      barcode: variant.barcode,
      weight: variant.weight,
      options: variant.options,
    );
  }
}

/// Data model for platform product SEO
@JsonSerializable()
class PlatformProductSEOModel extends PlatformProductSEO {
  const PlatformProductSEOModel({
    super.title,
    super.description,
  });

  factory PlatformProductSEOModel.fromJson(Map<String, dynamic> json) =>
      _$PlatformProductSEOModelFromJson(json);

  Map<String, dynamic> toJson() => _$PlatformProductSEOModelToJson(this);

  factory PlatformProductSEOModel.fromEntity(PlatformProductSEO seo) {
    return PlatformProductSEOModel(
      title: seo.title,
      description: seo.description,
    );
  }
}
