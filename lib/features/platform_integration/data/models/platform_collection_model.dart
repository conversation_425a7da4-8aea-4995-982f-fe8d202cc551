import 'package:json_annotation/json_annotation.dart';
import '../../domain/entities/platform_collection.dart';

part 'platform_collection_model.g.dart';

/// Data model for platform collection
@JsonSerializable()
class PlatformCollectionModel extends PlatformCollection {
  const PlatformCollectionModel({
    required super.id,
    required super.title,
    required super.description,
    required super.handle,
    required super.createdAt,
    required super.updatedAt,
    super.image,
    super.productCount,
    super.sortOrder,
    super.rules,
    super.seo,
  });

  /// Create from JSON
  factory PlatformCollectionModel.fromJson(Map<String, dynamic> json) =>
      _$PlatformCollectionModelFromJson(json);

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$PlatformCollectionModelToJson(this);

  /// Create from domain entity
  factory PlatformCollectionModel.fromEntity(PlatformCollection collection) {
    return PlatformCollectionModel(
      id: collection.id,
      title: collection.title,
      description: collection.description,
      handle: collection.handle,
      createdAt: collection.createdAt,
      updatedAt: collection.updatedAt,
      image: collection.image != null ? 
          PlatformCollectionImageModel.fromEntity(collection.image!) : null,
      productCount: collection.productCount,
      sortOrder: collection.sortOrder,
      rules: collection.rules.map((rule) => 
          PlatformCollectionRuleModel.fromEntity(rule)).toList(),
      seo: collection.seo != null ? 
          PlatformCollectionSEOModel.fromEntity(collection.seo!) : null,
    );
  }

  /// Convert to domain entity
  PlatformCollection toEntity() {
    return PlatformCollection(
      id: id,
      title: title,
      description: description,
      handle: handle,
      createdAt: createdAt,
      updatedAt: updatedAt,
      image: image,
      productCount: productCount,
      sortOrder: sortOrder,
      rules: rules,
      seo: seo,
    );
  }
}

/// Data model for platform collection image
@JsonSerializable()
class PlatformCollectionImageModel extends PlatformCollectionImage {
  const PlatformCollectionImageModel({
    required super.url,
    required super.altText,
    super.width,
    super.height,
  });

  factory PlatformCollectionImageModel.fromJson(Map<String, dynamic> json) =>
      _$PlatformCollectionImageModelFromJson(json);

  Map<String, dynamic> toJson() => _$PlatformCollectionImageModelToJson(this);

  factory PlatformCollectionImageModel.fromEntity(PlatformCollectionImage image) {
    return PlatformCollectionImageModel(
      url: image.url,
      altText: image.altText,
      width: image.width,
      height: image.height,
    );
  }
}

/// Data model for platform collection rule
@JsonSerializable()
class PlatformCollectionRuleModel extends PlatformCollectionRule {
  const PlatformCollectionRuleModel({
    required super.column,
    required super.relation,
    required super.condition,
  });

  factory PlatformCollectionRuleModel.fromJson(Map<String, dynamic> json) =>
      _$PlatformCollectionRuleModelFromJson(json);

  Map<String, dynamic> toJson() => _$PlatformCollectionRuleModelToJson(this);

  factory PlatformCollectionRuleModel.fromEntity(PlatformCollectionRule rule) {
    return PlatformCollectionRuleModel(
      column: rule.column,
      relation: rule.relation,
      condition: rule.condition,
    );
  }
}

/// Data model for platform collection SEO
@JsonSerializable()
class PlatformCollectionSEOModel extends PlatformCollectionSEO {
  const PlatformCollectionSEOModel({
    super.title,
    super.description,
  });

  factory PlatformCollectionSEOModel.fromJson(Map<String, dynamic> json) =>
      _$PlatformCollectionSEOModelFromJson(json);

  Map<String, dynamic> toJson() => _$PlatformCollectionSEOModelToJson(this);

  factory PlatformCollectionSEOModel.fromEntity(PlatformCollectionSEO seo) {
    return PlatformCollectionSEOModel(
      title: seo.title,
      description: seo.description,
    );
  }
}
