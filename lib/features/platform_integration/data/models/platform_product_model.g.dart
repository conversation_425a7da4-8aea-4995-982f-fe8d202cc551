// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'platform_product_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PlatformProductModel _$PlatformProductModelFromJson(
        Map<String, dynamic> json) =>
    PlatformProductModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      currency: json['currency'] as String,
      images: (json['images'] as List<dynamic>)
          .map((e) =>
              PlatformProductImageModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      variants: (json['variants'] as List<dynamic>)
          .map((e) =>
              PlatformProductVariantModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      isAvailable: json['isAvailable'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      compareAtPrice: (json['compareAtPrice'] as num?)?.toDouble(),
      vendor: json['vendor'] as String?,
      productType: json['productType'] as String?,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
      handle: json['handle'] as String?,
      seo: json['seo'] == null
          ? null
          : PlatformProductSEOModel.fromJson(
              json['seo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PlatformProductModelToJson(
        PlatformProductModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'price': instance.price,
      'currency': instance.currency,
      'images': instance.images,
      'variants': instance.variants,
      'isAvailable': instance.isAvailable,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'compareAtPrice': instance.compareAtPrice,
      'vendor': instance.vendor,
      'productType': instance.productType,
      'tags': instance.tags,
      'handle': instance.handle,
      'seo': instance.seo,
    };

PlatformProductImageModel _$PlatformProductImageModelFromJson(
        Map<String, dynamic> json) =>
    PlatformProductImageModel(
      id: json['id'] as String,
      url: json['url'] as String,
      altText: json['altText'] as String,
      width: (json['width'] as num?)?.toInt(),
      height: (json['height'] as num?)?.toInt(),
      position: (json['position'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$PlatformProductImageModelToJson(
        PlatformProductImageModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'url': instance.url,
      'altText': instance.altText,
      'width': instance.width,
      'height': instance.height,
      'position': instance.position,
    };

PlatformProductVariantModel _$PlatformProductVariantModelFromJson(
        Map<String, dynamic> json) =>
    PlatformProductVariantModel(
      id: json['id'] as String,
      title: json['title'] as String,
      price: (json['price'] as num).toDouble(),
      isAvailable: json['isAvailable'] as bool,
      inventoryQuantity: (json['inventoryQuantity'] as num).toInt(),
      compareAtPrice: (json['compareAtPrice'] as num?)?.toDouble(),
      sku: json['sku'] as String?,
      barcode: json['barcode'] as String?,
      weight: (json['weight'] as num?)?.toDouble(),
      options: (json['options'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as String),
          ) ??
          const {},
    );

Map<String, dynamic> _$PlatformProductVariantModelToJson(
        PlatformProductVariantModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'price': instance.price,
      'isAvailable': instance.isAvailable,
      'inventoryQuantity': instance.inventoryQuantity,
      'compareAtPrice': instance.compareAtPrice,
      'sku': instance.sku,
      'barcode': instance.barcode,
      'weight': instance.weight,
      'options': instance.options,
    };

PlatformProductSEOModel _$PlatformProductSEOModelFromJson(
        Map<String, dynamic> json) =>
    PlatformProductSEOModel(
      title: json['title'] as String?,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$PlatformProductSEOModelToJson(
        PlatformProductSEOModel instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
    };
