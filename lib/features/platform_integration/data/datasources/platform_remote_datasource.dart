import '../models/platform_product_model.dart';
import '../models/platform_collection_model.dart';

/// Abstract interface for platform remote data sources
abstract class PlatformRemoteDataSource {
  /// Get products with pagination and filtering
  Future<List<PlatformProductModel>> getProducts({
    int page = 1,
    int limit = 20,
    String? collectionId,
    String? query,
    Map<String, dynamic>? filters,
  });

  /// Get a single product by ID
  Future<PlatformProductModel> getProduct(String productId);

  /// Get collections/categories
  Future<List<PlatformCollectionModel>> getCollections({
    int page = 1,
    int limit = 20,
  });

  /// Get a single collection by ID
  Future<PlatformCollectionModel> getCollection(String collectionId);

  /// Search products
  Future<List<PlatformProductModel>> searchProducts({
    required String query,
    int page = 1,
    int limit = 20,
    Map<String, dynamic>? filters,
  });

  /// Get product recommendations
  Future<List<PlatformProductModel>> getRecommendedProducts({
    String? productId,
    int limit = 10,
  });

  /// Check product availability
  Future<bool> checkProductAvailability(
    String productId, {
    String? variantId,
    int quantity = 1,
  });

  /// Get platform-specific configuration
  Future<Map<String, dynamic>> getPlatformConfig();

  /// Test platform connection
  Future<bool> testConnection();
}
