import 'package:logger/logger.dart';

/// Custom logger configuration for the application
class AppLogger {
  static Logger? _instance;
  
  /// Get the singleton logger instance
  static Logger get instance {
    _instance ??= Logger(
      printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: true,
      ),
      filter: ProductionFilter(),
    );
    return _instance!;
  }

  /// Initialize logger with custom configuration
  static void initialize({
    LogPrinter? printer,
    LogFilter? filter,
    LogOutput? output,
  }) {
    _instance = Logger(
      printer: printer ?? PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: true,
      ),
      filter: filter ?? ProductionFilter(),
      output: output,
    );
  }

  /// Log debug message
  static void debug(dynamic message, {dynamic error, StackTrace? stackTrace}) {
    instance.d(message, error: error, stackTrace: stackTrace);
  }

  /// Log info message
  static void info(dynamic message, {dynamic error, StackTrace? stackTrace}) {
    instance.i(message, error: error, stackTrace: stackTrace);
  }

  /// Log warning message
  static void warning(dynamic message, {dynamic error, StackTrace? stackTrace}) {
    instance.w(message, error: error, stackTrace: stackTrace);
  }

  /// Log error message
  static void error(dynamic message, {dynamic error, StackTrace? stackTrace}) {
    instance.e(message, error: error, stackTrace: stackTrace);
  }

  /// Log fatal message
  static void fatal(dynamic message, {dynamic error, StackTrace? stackTrace}) {
    instance.f(message, error: error, stackTrace: stackTrace);
  }
}

/// Custom log filter for production builds
class ProductionFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    // In debug mode, log everything
    if (const bool.fromEnvironment('dart.vm.product') == false) {
      return true;
    }
    
    // In production, only log warnings and above
    return event.level.index >= Level.warning.index;
  }
}

/// Custom log printer for structured logging
class StructuredLogPrinter extends LogPrinter {
  @override
  List<String> log(LogEvent event) {
    final timestamp = DateTime.now().toIso8601String();
    final level = event.level.name.toUpperCase();
    final message = event.message;
    
    final logEntry = {
      'timestamp': timestamp,
      'level': level,
      'message': message,
      if (event.error != null) 'error': event.error.toString(),
      if (event.stackTrace != null) 'stackTrace': event.stackTrace.toString(),
    };
    
    return [logEntry.toString()];
  }
}
