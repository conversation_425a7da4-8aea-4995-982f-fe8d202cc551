import 'package:intl/intl.dart';

import '../../constants/app_constants.dart';

/// Helper class for currency operations
class CurrencyHelper {
  /// Format currency with symbol and decimal places
  static String formatCurrency(
    double amount, {
    String symbol = AppConstants.currencySymbol,
    int decimalPlaces = AppConstants.currencyDecimalPlaces,
    String locale = 'en_US',
  }) {
    final formatter = NumberFormat.currency(
      symbol: symbol,
      decimalDigits: decimalPlaces,
      locale: locale,
    );
    return formatter.format(amount);
  }
  
  /// Format currency without symbol
  static String formatCurrencyWithoutSymbol(
    double amount, {
    int decimalPlaces = AppConstants.currencyDecimalPlaces,
    String locale = 'en_US',
  }) {
    final formatter = NumberFormat.currency(
      symbol: '',
      decimalDigits: decimalPlaces,
      locale: locale,
    );
    return formatter.format(amount).trim();
  }
  
  /// Parse currency string to double
  static double? parseCurrency(String currencyString) {
    try {
      // Remove currency symbols and spaces
      final cleanString = currencyString
          .replaceAll(RegExp(r'[^\d.,\-]'), '')
          .replaceAll(',', '');
      return double.tryParse(cleanString);
    } catch (e) {
      return null;
    }
  }
  
  /// Convert between currencies (placeholder - would integrate with exchange rate API)
  static double convertCurrency(
    double amount,
    String fromCurrency,
    String toCurrency, {
    Map<String, double>? exchangeRates,
  }) {
    // This is a placeholder implementation
    // In a real app, you would integrate with an exchange rate API
    if (fromCurrency == toCurrency) return amount;
    
    // Use provided exchange rates or default rates
    final rates = exchangeRates ?? _getDefaultExchangeRates();
    
    // Convert to USD first, then to target currency
    final usdAmount = fromCurrency == 'USD' ? amount : amount / (rates[fromCurrency] ?? 1.0);
    final convertedAmount = toCurrency == 'USD' ? usdAmount : usdAmount * (rates[toCurrency] ?? 1.0);
    
    return convertedAmount;
  }
  
  /// Get currency symbol for currency code
  static String getCurrencySymbol(String currencyCode) {
    const symbols = {
      'USD': '\$',
      'EUR': '€',
      'GBP': '£',
      'JPY': '¥',
      'CNY': '¥',
      'INR': '₹',
      'CAD': 'C\$',
      'AUD': 'A\$',
      'CHF': 'CHF',
      'SEK': 'kr',
      'NOK': 'kr',
      'DKK': 'kr',
      'PLN': 'zł',
      'CZK': 'Kč',
      'HUF': 'Ft',
      'RUB': '₽',
      'BRL': 'R\$',
      'MXN': '\$',
      'ZAR': 'R',
      'KRW': '₩',
      'SGD': 'S\$',
      'HKD': 'HK\$',
      'NZD': 'NZ\$',
      'THB': '฿',
      'MYR': 'RM',
      'PHP': '₱',
      'IDR': 'Rp',
      'VND': '₫',
    };
    
    return symbols[currencyCode.toUpperCase()] ?? currencyCode;
  }
  
  /// Get currency name for currency code
  static String getCurrencyName(String currencyCode) {
    const names = {
      'USD': 'US Dollar',
      'EUR': 'Euro',
      'GBP': 'British Pound',
      'JPY': 'Japanese Yen',
      'CNY': 'Chinese Yuan',
      'INR': 'Indian Rupee',
      'CAD': 'Canadian Dollar',
      'AUD': 'Australian Dollar',
      'CHF': 'Swiss Franc',
      'SEK': 'Swedish Krona',
      'NOK': 'Norwegian Krone',
      'DKK': 'Danish Krone',
      'PLN': 'Polish Zloty',
      'CZK': 'Czech Koruna',
      'HUF': 'Hungarian Forint',
      'RUB': 'Russian Ruble',
      'BRL': 'Brazilian Real',
      'MXN': 'Mexican Peso',
      'ZAR': 'South African Rand',
      'KRW': 'South Korean Won',
      'SGD': 'Singapore Dollar',
      'HKD': 'Hong Kong Dollar',
      'NZD': 'New Zealand Dollar',
      'THB': 'Thai Baht',
      'MYR': 'Malaysian Ringgit',
      'PHP': 'Philippine Peso',
      'IDR': 'Indonesian Rupiah',
      'VND': 'Vietnamese Dong',
    };
    
    return names[currencyCode.toUpperCase()] ?? currencyCode;
  }
  
  /// Format price range
  static String formatPriceRange(
    double minPrice,
    double maxPrice, {
    String symbol = AppConstants.currencySymbol,
    int decimalPlaces = AppConstants.currencyDecimalPlaces,
  }) {
    if (minPrice == maxPrice) {
      return formatCurrency(minPrice, symbol: symbol, decimalPlaces: decimalPlaces);
    }
    
    final min = formatCurrency(minPrice, symbol: symbol, decimalPlaces: decimalPlaces);
    final max = formatCurrency(maxPrice, symbol: symbol, decimalPlaces: decimalPlaces);
    
    return '$min - $max';
  }
  
  /// Calculate discount percentage
  static double calculateDiscountPercentage(double originalPrice, double discountedPrice) {
    if (originalPrice <= 0) return 0;
    return ((originalPrice - discountedPrice) / originalPrice) * 100;
  }
  
  /// Calculate discount amount
  static double calculateDiscountAmount(double originalPrice, double discountPercentage) {
    return originalPrice * (discountPercentage / 100);
  }
  
  /// Apply discount to price
  static double applyDiscount(double originalPrice, double discountPercentage) {
    final discountAmount = calculateDiscountAmount(originalPrice, discountPercentage);
    return originalPrice - discountAmount;
  }
  
  /// Format discount percentage
  static String formatDiscountPercentage(double percentage) {
    return '${percentage.toStringAsFixed(0)}% OFF';
  }
  
  /// Check if price is valid
  static bool isValidPrice(double price) {
    return price >= 0 && price.isFinite;
  }
  
  /// Round price to currency precision
  static double roundPrice(double price, {int decimalPlaces = AppConstants.currencyDecimalPlaces}) {
    final factor = 1.0 * (10 * decimalPlaces);
    return (price * factor).round() / factor;
  }
  
  /// Get default exchange rates (placeholder)
  static Map<String, double> _getDefaultExchangeRates() {
    // This would typically come from an API
    return {
      'USD': 1.0,
      'EUR': 0.85,
      'GBP': 0.73,
      'JPY': 110.0,
      'CNY': 6.45,
      'INR': 74.5,
      'CAD': 1.25,
      'AUD': 1.35,
      'CHF': 0.92,
      'SEK': 8.5,
      'NOK': 8.8,
      'DKK': 6.3,
      'PLN': 3.9,
      'CZK': 21.5,
      'HUF': 295.0,
      'RUB': 73.0,
      'BRL': 5.2,
      'MXN': 20.0,
      'ZAR': 14.5,
      'KRW': 1180.0,
      'SGD': 1.35,
      'HKD': 7.8,
      'NZD': 1.42,
      'THB': 31.0,
      'MYR': 4.15,
      'PHP': 50.0,
      'IDR': 14250.0,
      'VND': 23000.0,
    };
  }
  
  /// Get supported currencies
  static List<String> getSupportedCurrencies() {
    return [
      'USD', 'EUR', 'GBP', 'JPY', 'CNY', 'INR', 'CAD', 'AUD', 'CHF',
      'SEK', 'NOK', 'DKK', 'PLN', 'CZK', 'HUF', 'RUB', 'BRL', 'MXN',
      'ZAR', 'KRW', 'SGD', 'HKD', 'NZD', 'THB', 'MYR', 'PHP', 'IDR', 'VND',
    ];
  }
  
  /// Check if currency is supported
  static bool isSupportedCurrency(String currencyCode) {
    return getSupportedCurrencies().contains(currencyCode.toUpperCase());
  }
}
