/// Storage keys for local storage and secure storage
class StorageKeys {
  // Authentication Keys (Secure Storage)
  static const String authToken = 'auth_token';
  static const String refreshToken = 'refresh_token';
  static const String biometricEnabled = 'biometric_enabled';
  static const String userCredentials = 'user_credentials';
  
  // User Data Keys (Local Storage)
  static const String userId = 'user_id';
  static const String userEmail = 'user_email';
  static const String userName = 'user_name';
  static const String userProfile = 'user_profile';
  static const String userPreferences = 'user_preferences';
  
  // App Configuration Keys (Local Storage)
  static const String selectedPlatform = 'selected_platform';
  static const String platformConfig = 'platform_config';
  static const String themeMode = 'theme_mode';
  static const String language = 'language';
  static const String locale = 'locale';
  
  // Onboarding and First Launch (Local Storage)
  static const String firstLaunch = 'first_launch';
  static const String onboardingCompleted = 'onboarding_completed';
  static const String tutorialCompleted = 'tutorial_completed';
  static const String appVersion = 'app_version';
  
  // Cart and Shopping (Local Storage)
  static const String cartItems = 'cart_items';
  static const String cartTimestamp = 'cart_timestamp';
  static const String wishlistItems = 'wishlist_items';
  static const String recentlyViewed = 'recently_viewed';
  
  // Search and Filters (Local Storage)
  static const String searchHistory = 'search_history';
  static const String savedFilters = 'saved_filters';
  static const String sortPreferences = 'sort_preferences';
  
  // Cache Keys (Cache Storage)
  static const String productsCache = 'products_cache';
  static const String categoriesCache = 'categories_cache';
  static const String productDetailsCache = 'product_details_cache';
  static const String searchResultsCache = 'search_results_cache';
  
  // Platform-specific Cache Keys
  static const String shopifyProductsCache = 'shopify_products_cache';
  static const String shopifyCategoriesCache = 'shopify_categories_cache';
  static const String woocommerceProductsCache = 'woocommerce_products_cache';
  static const String woocommerceCategoriesCache = 'woocommerce_categories_cache';
  static const String bigcommerceProductsCache = 'bigcommerce_products_cache';
  static const String bigcommerceCategoriesCache = 'bigcommerce_categories_cache';
  
  // Analytics and Tracking (Local Storage)
  static const String analyticsEnabled = 'analytics_enabled';
  static const String crashReportingEnabled = 'crash_reporting_enabled';
  static const String performanceMonitoringEnabled = 'performance_monitoring_enabled';
  
  // Notification Settings (Local Storage)
  static const String pushNotificationsEnabled = 'push_notifications_enabled';
  static const String notificationToken = 'notification_token';
  static const String notificationPreferences = 'notification_preferences';
  
  // Offline Mode (Local Storage)
  static const String offlineModeEnabled = 'offline_mode_enabled';
  static const String lastSyncTimestamp = 'last_sync_timestamp';
  static const String pendingSyncData = 'pending_sync_data';
  
  // Debug and Development (Local Storage)
  static const String debugModeEnabled = 'debug_mode_enabled';
  static const String mockDataEnabled = 'mock_data_enabled';
  static const String apiLoggingEnabled = 'api_logging_enabled';
  
  /// Get cache key for specific platform and data type
  static String getPlatformCacheKey(String platform, String dataType) {
    return '${platform}_${dataType}_cache';
  }
  
  /// Get user-specific key
  static String getUserSpecificKey(String userId, String key) {
    return '${userId}_$key';
  }
  
  /// Get platform-specific key
  static String getPlatformSpecificKey(String platform, String key) {
    return '${platform}_$key';
  }
  
  /// Get timestamped key for cache invalidation
  static String getTimestampedKey(String key) {
    return '${key}_timestamp';
  }
}
