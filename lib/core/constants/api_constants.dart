/// API-related constants and configuration
class ApiConstants {
  // HTTP Status Codes
  static const int statusOk = 200;
  static const int statusCreated = 201;
  static const int statusAccepted = 202;
  static const int statusNoContent = 204;
  static const int statusBadRequest = 400;
  static const int statusUnauthorized = 401;
  static const int statusForbidden = 403;
  static const int statusNotFound = 404;
  static const int statusMethodNotAllowed = 405;
  static const int statusConflict = 409;
  static const int statusUnprocessableEntity = 422;
  static const int statusTooManyRequests = 429;
  static const int statusInternalServerError = 500;
  static const int statusBadGateway = 502;
  static const int statusServiceUnavailable = 503;
  static const int statusGatewayTimeout = 504;
  
  // HTTP Headers
  static const String headerContentType = 'Content-Type';
  static const String headerAccept = 'Accept';
  static const String headerAuthorization = 'Authorization';
  static const String headerUserAgent = 'User-Agent';
  static const String headerAcceptLanguage = 'Accept-Language';
  static const String headerCacheControl = 'Cache-Control';
  static const String headerIfNoneMatch = 'If-None-Match';
  static const String headerETag = 'ETag';
  
  // Content Types
  static const String contentTypeJson = 'application/json';
  static const String contentTypeFormData = 'multipart/form-data';
  static const String contentTypeUrlEncoded = 'application/x-www-form-urlencoded';
  
  // API Versions
  static const String shopifyApiVersion = '2023-10';
  static const String woocommerceApiVersion = 'v3';
  static const String bigcommerceApiVersion = 'v3';
  
  // Query Parameters
  static const String paramPage = 'page';
  static const String paramLimit = 'limit';
  static const String paramOffset = 'offset';
  static const String paramSort = 'sort';
  static const String paramOrder = 'order';
  static const String paramSearch = 'search';
  static const String paramFilter = 'filter';
  static const String paramFields = 'fields';
  static const String paramInclude = 'include';
  static const String paramExpand = 'expand';
  
  // Sort Orders
  static const String sortAsc = 'asc';
  static const String sortDesc = 'desc';
  
  // Common Sort Fields
  static const String sortByName = 'name';
  static const String sortByPrice = 'price';
  static const String sortByDate = 'date';
  static const String sortByPopularity = 'popularity';
  static const String sortByRating = 'rating';
  static const String sortByRelevance = 'relevance';
  
  // Platform-specific Headers
  static const Map<String, String> shopifyHeaders = {
    'X-Shopify-Access-Token': '',
    'X-Shopify-Storefront-Access-Token': '',
  };
  
  static const Map<String, String> woocommerceHeaders = {
    'Authorization': 'Basic ',
  };
  
  static const Map<String, String> bigcommerceHeaders = {
    'X-Auth-Token': '',
    'X-Auth-Client': '',
  };
  
  // Rate Limiting
  static const int maxRequestsPerMinute = 60;
  static const int maxRequestsPerHour = 3600;
  static const Duration rateLimitWindow = Duration(minutes: 1);
  
  // Retry Configuration
  static const int maxRetryAttempts = 3;
  static const Duration initialRetryDelay = Duration(seconds: 1);
  static const double retryDelayMultiplier = 2.0;
  static const Duration maxRetryDelay = Duration(seconds: 30);
  
  // Cache Headers
  static const String cacheControlNoCache = 'no-cache';
  static const String cacheControlNoStore = 'no-store';
  static const String cacheControlMaxAge = 'max-age=';
  static const String cacheControlMustRevalidate = 'must-revalidate';
  
  // Error Codes
  static const String errorCodeNetworkError = 'NETWORK_ERROR';
  static const String errorCodeTimeout = 'TIMEOUT';
  static const String errorCodeUnauthorized = 'UNAUTHORIZED';
  static const String errorCodeForbidden = 'FORBIDDEN';
  static const String errorCodeNotFound = 'NOT_FOUND';
  static const String errorCodeValidation = 'VALIDATION_ERROR';
  static const String errorCodeServerError = 'SERVER_ERROR';
  static const String errorCodeUnknown = 'UNKNOWN_ERROR';
  
  // Success Messages
  static const String messageSuccess = 'Operation completed successfully';
  static const String messageCreated = 'Resource created successfully';
  static const String messageUpdated = 'Resource updated successfully';
  static const String messageDeleted = 'Resource deleted successfully';
  
  // Error Messages
  static const String messageNetworkError = 'Network error occurred';
  static const String messageTimeout = 'Request timeout';
  static const String messageUnauthorized = 'Unauthorized access';
  static const String messageForbidden = 'Access forbidden';
  static const String messageNotFound = 'Resource not found';
  static const String messageValidationError = 'Validation error';
  static const String messageServerError = 'Server error occurred';
  static const String messageUnknownError = 'Unknown error occurred';
  
  /// Get user agent string for API requests
  static String getUserAgent() {
    return 'EcommerceApp/1.0.0 (Flutter)';
  }
  
  /// Get authorization header value
  static String getBearerToken(String token) {
    return 'Bearer $token';
  }
  
  /// Get basic auth header value
  static String getBasicAuth(String username, String password) {
    final credentials = '$username:$password';
    final encoded = credentials; // Base64 encoding would be done here
    return 'Basic $encoded';
  }
  
  /// Check if status code indicates success
  static bool isSuccessStatusCode(int statusCode) {
    return statusCode >= 200 && statusCode < 300;
  }
  
  /// Check if status code indicates client error
  static bool isClientError(int statusCode) {
    return statusCode >= 400 && statusCode < 500;
  }
  
  /// Check if status code indicates server error
  static bool isServerError(int statusCode) {
    return statusCode >= 500 && statusCode < 600;
  }
}
