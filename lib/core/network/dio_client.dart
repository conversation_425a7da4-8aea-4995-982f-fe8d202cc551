import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

import '../errors/error_handler.dart';
import '../errors/exceptions.dart';
import 'api_endpoints.dart';

/// Interface for HTTP client
abstract class HttpClient {
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  });

  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  });

  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  });

  Future<ApiResponse<T>> patch<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  });

  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  });
}

/// Dio-based HTTP client implementation
class DioClient implements HttpClient {
  DioClient({
    required Dio dio,
    required ErrorHandler errorHandler,
    required Logger logger,
  })  : _dio = dio,
        _errorHandler = errorHandler,
        _logger = logger {
    _setupInterceptors();
  }

  final Dio _dio;
  final ErrorHandler _errorHandler;
  final Logger _logger;

  void _setupInterceptors() {
    _dio.interceptors.addAll([
      LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: false,
        error: true,
        logPrint: (object) => _logger.d(object),
      ),
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // Add common headers
          options.headers.addAll({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          });
          handler.next(options);
        },
        onError: (error, handler) {
          _logger.e('HTTP Error: ${error.message}', error: error);
          handler.next(error);
        },
      ),
    ]);
  }

  @override
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.get<Map<String, dynamic>>(
        endpoint,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );
      return _handleResponse<T>(response);
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      _logger.e('Unexpected error in GET request', error: e);
      throw NetworkException(
        message: 'Unexpected error occurred: $e',
        stackTrace: StackTrace.current,
      );
    }
  }

  @override
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.post<Map<String, dynamic>>(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );
      return _handleResponse<T>(response);
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      _logger.e('Unexpected error in POST request', error: e);
      throw NetworkException(
        message: 'Unexpected error occurred: $e',
        stackTrace: StackTrace.current,
      );
    }
  }

  @override
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.put<Map<String, dynamic>>(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );
      return _handleResponse<T>(response);
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      _logger.e('Unexpected error in PUT request', error: e);
      throw NetworkException(
        message: 'Unexpected error occurred: $e',
        stackTrace: StackTrace.current,
      );
    }
  }

  @override
  Future<ApiResponse<T>> patch<T>(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.patch<Map<String, dynamic>>(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );
      return _handleResponse<T>(response);
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      _logger.e('Unexpected error in PATCH request', error: e);
      throw NetworkException(
        message: 'Unexpected error occurred: $e',
        stackTrace: StackTrace.current,
      );
    }
  }

  @override
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    Map<String, String>? headers,
  }) async {
    try {
      final response = await _dio.delete<Map<String, dynamic>>(
        endpoint,
        queryParameters: queryParameters,
        options: Options(headers: headers),
      );
      return _handleResponse<T>(response);
    } on DioException catch (e) {
      throw _handleDioException(e);
    } catch (e) {
      _logger.e('Unexpected error in DELETE request', error: e);
      throw NetworkException(
        message: 'Unexpected error occurred: $e',
        stackTrace: StackTrace.current,
      );
    }
  }

  ApiResponse<T> _handleResponse<T>(Response<Map<String, dynamic>> response) {
    return ApiResponse<T>(
      data: response.data?['data'] as T?,
      statusCode: response.statusCode ?? 0,
      message: response.data?['message'] as String?,
      errors: (response.data?['errors'] as List<dynamic>?)
          ?.map((e) => e.toString())
          .toList(),
    );
  }

  Exception _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const NetworkException(
          message: 'Connection timeout',
          code: 'TIMEOUT',
        );
      case DioExceptionType.badResponse:
        return ServerException(
          message: 'Server error: ${e.response?.statusCode}',
          code: e.response?.statusCode.toString(),
        );
      case DioExceptionType.cancel:
        return const NetworkException(
          message: 'Request cancelled',
          code: 'CANCELLED',
        );
      case DioExceptionType.connectionError:
        return const NetworkException(
          message: 'No internet connection',
          code: 'NO_CONNECTION',
        );
      case DioExceptionType.badCertificate:
        return const NetworkException(
          message: 'Certificate error',
          code: 'BAD_CERTIFICATE',
        );
      case DioExceptionType.unknown:
      default:
        return NetworkException(
          message: e.message ?? 'Unknown network error',
          code: 'UNKNOWN',
        );
    }
  }
}
