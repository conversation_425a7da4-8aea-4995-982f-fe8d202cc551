/// API endpoints configuration
class ApiEndpoints {
  // Base URLs for different platforms
  static const String shopifyBaseUrl = 'https://your-shop.myshopify.com/api/2023-10';
  static const String wooCommerceBaseUrl = 'https://your-site.com/wp-json/wc/v3';
  static const String bigCommerceBaseUrl = 'https://api.bigcommerce.com/stores/your-store/v3';
  
  // Common endpoints
  static const String products = '/products';
  static const String categories = '/categories';
  static const String cart = '/cart';
  static const String checkout = '/checkout';
  static const String orders = '/orders';
  static const String customers = '/customers';
  static const String auth = '/auth';
  
  // Product endpoints
  static const String productDetails = '/products/{id}';
  static const String productSearch = '/products/search';
  static const String productsByCategory = '/categories/{id}/products';
  
  // Cart endpoints
  static const String addToCart = '/cart/add';
  static const String updateCartItem = '/cart/items/{id}';
  static const String removeFromCart = '/cart/items/{id}';
  static const String clearCart = '/cart/clear';
  
  // Checkout endpoints
  static const String createCheckout = '/checkout/create';
  static const String updateCheckout = '/checkout/{id}';
  static const String completeCheckout = '/checkout/{id}/complete';
  static const String applyCoupon = '/checkout/{id}/coupons';
  
  // Authentication endpoints
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String logout = '/auth/logout';
  static const String refreshToken = '/auth/refresh';
  static const String forgotPassword = '/auth/forgot-password';
  
  /// Replace path parameters with actual values
  static String replacePathParams(String endpoint, Map<String, String> params) {
    String result = endpoint;
    params.forEach((key, value) {
      result = result.replaceAll('{$key}', value);
    });
    return result;
  }
}

/// HTTP methods
enum HttpMethod {
  get,
  post,
  put,
  patch,
  delete,
}

/// API response wrapper
class ApiResponse<T> {
  const ApiResponse({
    required this.data,
    required this.statusCode,
    this.message,
    this.errors,
  });

  final T? data;
  final int statusCode;
  final String? message;
  final List<String>? errors;

  bool get isSuccess => statusCode >= 200 && statusCode < 300;
  bool get hasErrors => errors != null && errors!.isNotEmpty;
}

/// Pagination metadata
class PaginationMeta {
  const PaginationMeta({
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.itemsPerPage,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int itemsPerPage;
  final bool hasNextPage;
  final bool hasPreviousPage;

  factory PaginationMeta.fromJson(Map<String, dynamic> json) {
    return PaginationMeta(
      currentPage: json['current_page'] as int? ?? 1,
      totalPages: json['total_pages'] as int? ?? 1,
      totalItems: json['total_items'] as int? ?? 0,
      itemsPerPage: json['items_per_page'] as int? ?? 20,
      hasNextPage: json['has_next_page'] as bool? ?? false,
      hasPreviousPage: json['has_previous_page'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'current_page': currentPage,
      'total_pages': totalPages,
      'total_items': totalItems,
      'items_per_page': itemsPerPage,
      'has_next_page': hasNextPage,
      'has_previous_page': hasPreviousPage,
    };
  }
}

/// Paginated API response
class PaginatedApiResponse<T> extends ApiResponse<List<T>> {
  const PaginatedApiResponse({
    required super.data,
    required super.statusCode,
    required this.meta,
    super.message,
    super.errors,
  });

  final PaginationMeta meta;
}
