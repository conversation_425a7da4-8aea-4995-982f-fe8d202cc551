import 'package:get_it/get_it.dart';
import '../../features/platform_integration/data/datasources/platform_remote_datasource.dart';
import '../../features/platform_integration/data/datasources/shopify_remote_datasource.dart';
import '../../features/platform_integration/data/datasources/mock_remote_datasource.dart';
import '../../features/platform_integration/data/repositories/platform_repository_impl.dart';
import '../../features/platform_integration/domain/repositories/platform_repository.dart';
import 'platform_config.dart';
import 'platform_type.dart';

/// Factory class for creating platform-specific implementations
class PlatformFactory {
  static final GetIt _locator = GetIt.instance;

  /// Register platform-specific dependencies based on configuration
  static Future<void> registerPlatformDependencies(PlatformConfig config) async {
    // Unregister existing instances if any
    await _unregisterExistingDependencies();

    // Register platform-specific remote data source
    final remoteDataSource = _createRemoteDataSource(config);
    _locator.registerLazySingleton<PlatformRemoteDataSource>(() => remoteDataSource);

    // Register repository implementation
    _locator.registerLazySingleton<PlatformRepository>(
      () => PlatformRepositoryImpl(
        remoteDataSource: _locator<PlatformRemoteDataSource>(),
        config: config,
      ),
    );
  }

  /// Create platform-specific remote data source
  static PlatformRemoteDataSource _createRemoteDataSource(PlatformConfig config) {
    switch (config.platformType) {
      case PlatformType.shopify:
        return ShopifyRemoteDataSource(config);
      case PlatformType.woocommerce:
        // TODO: Implement WooCommerce data source
        throw UnimplementedError('WooCommerce integration not yet implemented');
      case PlatformType.magento:
        // TODO: Implement Magento data source
        throw UnimplementedError('Magento integration not yet implemented');
      case PlatformType.mock:
        return MockRemoteDataSource(config);
    }
  }

  /// Unregister existing platform dependencies
  static Future<void> _unregisterExistingDependencies() async {
    if (_locator.isRegistered<PlatformRepository>()) {
      await _locator.unregister<PlatformRepository>();
    }
    if (_locator.isRegistered<PlatformRemoteDataSource>()) {
      await _locator.unregister<PlatformRemoteDataSource>();
    }
  }

  /// Switch to a different platform
  static Future<void> switchPlatform(PlatformConfig newConfig) async {
    await registerPlatformDependencies(newConfig);
  }

  /// Get current platform repository
  static PlatformRepository get repository => _locator<PlatformRepository>();

  /// Check if platform is registered
  static bool get isPlatformRegistered => _locator.isRegistered<PlatformRepository>();

  /// Get supported platforms
  static List<PlatformType> get supportedPlatforms => [
        PlatformType.shopify,
        PlatformType.mock,
        // Add more platforms as they are implemented
      ];

  /// Create default configuration for a platform type
  static PlatformConfig createDefaultConfig(PlatformType platformType) {
    switch (platformType) {
      case PlatformType.shopify:
        return PlatformConfig.shopify(
          shopDomain: 'your-shop-domain',
          accessToken: 'your-access-token',
        );
      case PlatformType.woocommerce:
        return PlatformConfig.woocommerce(
          baseUrl: 'https://your-site.com',
          consumerKey: 'your-consumer-key',
          consumerSecret: 'your-consumer-secret',
        );
      case PlatformType.magento:
        // TODO: Implement Magento default config
        throw UnimplementedError('Magento configuration not yet implemented');
      case PlatformType.mock:
        return PlatformConfig.mock();
    }
  }
}
