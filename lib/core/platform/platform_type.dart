/// Enumeration of supported ecommerce platforms
enum PlatformType {
  shopify('shopify', 'Shopify'),
  woocommerce('woocommerce', 'WooCommerce'),
  magento('magento', 'Magento'),
  mock('mock', 'Mock Platform');

  const PlatformType(this.id, this.displayName);

  final String id;
  final String displayName;

  /// Get platform type from string ID
  static PlatformType fromId(String id) {
    return PlatformType.values.firstWhere(
      (platform) => platform.id == id,
      orElse: () => PlatformType.mock,
    );
  }

  /// Check if platform is mock/development platform
  bool get isMock => this == PlatformType.mock;

  /// Check if platform supports real-time inventory
  bool get supportsRealTimeInventory {
    switch (this) {
      case PlatformType.shopify:
      case PlatformType.woocommerce:
        return true;
      case PlatformType.magento:
      case PlatformType.mock:
        return false;
    }
  }

  /// Check if platform supports webhooks
  bool get supportsWebhooks {
    switch (this) {
      case PlatformType.shopify:
      case PlatformType.woocommerce:
      case PlatformType.magento:
        return true;
      case PlatformType.mock:
        return false;
    }
  }
}
