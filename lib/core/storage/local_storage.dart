/// Interface for local storage operations
abstract class LocalStorage {
  /// Initialize the storage
  Future<void> init();
  
  /// Store a value with a key
  Future<void> setString(String key, String value);
  Future<void> setInt(String key, int value);
  Future<void> setBool(String key, bool value);
  Future<void> setDouble(String key, double value);
  Future<void> setStringList(String key, List<String> value);
  
  /// Retrieve a value by key
  Future<String?> getString(String key);
  Future<int?> getInt(String key);
  Future<bool?> getBool(String key);
  Future<double?> getDouble(String key);
  Future<List<String>?> getStringList(String key);
  
  /// Check if a key exists
  Future<bool> containsKey(String key);
  
  /// Remove a value by key
  Future<void> remove(String key);
  
  /// Clear all stored values
  Future<void> clear();
  
  /// Get all keys
  Future<Set<String>> getKeys();
}

/// SharedPreferences-based implementation of LocalStorage
class SharedPreferencesStorage implements LocalStorage {
  // Implementation will be added after setting up SharedPreferences
  
  @override
  Future<void> init() async {
    // TODO: Initialize SharedPreferences
  }

  @override
  Future<void> setString(String key, String value) async {
    // TODO: Implement using SharedPreferences
  }

  @override
  Future<void> setInt(String key, int value) async {
    // TODO: Implement using SharedPreferences
  }

  @override
  Future<void> setBool(String key, bool value) async {
    // TODO: Implement using SharedPreferences
  }

  @override
  Future<void> setDouble(String key, double value) async {
    // TODO: Implement using SharedPreferences
  }

  @override
  Future<void> setStringList(String key, List<String> value) async {
    // TODO: Implement using SharedPreferences
  }

  @override
  Future<String?> getString(String key) async {
    // TODO: Implement using SharedPreferences
    return null;
  }

  @override
  Future<int?> getInt(String key) async {
    // TODO: Implement using SharedPreferences
    return null;
  }

  @override
  Future<bool?> getBool(String key) async {
    // TODO: Implement using SharedPreferences
    return null;
  }

  @override
  Future<double?> getDouble(String key) async {
    // TODO: Implement using SharedPreferences
    return null;
  }

  @override
  Future<List<String>?> getStringList(String key) async {
    // TODO: Implement using SharedPreferences
    return null;
  }

  @override
  Future<bool> containsKey(String key) async {
    // TODO: Implement using SharedPreferences
    return false;
  }

  @override
  Future<void> remove(String key) async {
    // TODO: Implement using SharedPreferences
  }

  @override
  Future<void> clear() async {
    // TODO: Implement using SharedPreferences
  }

  @override
  Future<Set<String>> getKeys() async {
    // TODO: Implement using SharedPreferences
    return <String>{};
  }
}
