// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:get_it/get_it.dart' as _i1;

extension GetItInjectableX on _i1.GetIt {
  // For now, this is a placeholder implementation
  // In a real project, this would be generated by injectable_generator
  _i1.GetIt init() {
    // This method will be properly implemented when we run code generation
    // For now, return the GetIt instance to prevent compilation errors
    return this;
  }
}
