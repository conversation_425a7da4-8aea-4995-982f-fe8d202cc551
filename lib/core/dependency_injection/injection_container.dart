import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:logger/logger.dart';

import '../constants/app_constants.dart';
import '../errors/error_handler.dart';
import '../network/dio_client.dart';
import '../network/network_info.dart';
import '../storage/cache_manager.dart';
import '../storage/local_storage.dart';
import '../storage/secure_storage.dart';
import '../utils/logger.dart';
import 'injection_container.config.dart';

/// Service locator instance
final GetIt getIt = GetIt.instance;

/// Configure dependency injection
@InjectableInit()
Future<void> configureDependencies() async {
  getIt.init();
}

/// Register core dependencies manually (before code generation)
Future<void> registerCoreDependencies() async {
  // Logger
  getIt.registerLazySingleton<Logger>(() => AppLogger.instance);

  // Dio configuration
  getIt.registerLazySingleton<Dio>(() {
    final dio = Dio();
    dio.options = BaseOptions(
      connectTimeout: AppConstants.connectTimeout,
      receiveTimeout: AppConstants.receiveTimeout,
      sendTimeout: AppConstants.defaultTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );
    return dio;
  });

  // Error Handler
  getIt.registerLazySingleton<ErrorHandler>(
    () => ErrorHandlerImpl(logger: getIt<Logger>()),
  );

  // Network Info
  getIt.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl());

  // HTTP Client
  getIt.registerLazySingleton<HttpClient>(
    () => DioClient(
      dio: getIt<Dio>(),
      errorHandler: getIt<ErrorHandler>(),
      logger: getIt<Logger>(),
    ),
  );

  // Storage
  getIt.registerLazySingleton<LocalStorage>(() => SharedPreferencesStorage());
  getIt.registerLazySingleton<SecureStorage>(() => FlutterSecureStorageImpl());
  getIt.registerLazySingleton<CacheManager>(() => HiveCacheManager());

  // Initialize storage
  await getIt<LocalStorage>().init();
  await getIt<SecureStorage>().init();
  await getIt<CacheManager>().init();
}

/// Reset all dependencies (useful for testing)
Future<void> resetDependencies() async {
  await getIt.reset();
}

/// Register test dependencies
Future<void> registerTestDependencies() async {
  // This will be used for testing with mock implementations
  await resetDependencies();
  // Register mock implementations here
}
