import 'package:get_it/get_it.dart';

/// Service locator for easy access to dependencies
class ServiceLocator {
  static final GetIt _getIt = GetIt.instance;

  /// Get a dependency by type
  static T get<T extends Object>() => _getIt.get<T>();

  /// Get a dependency by type with optional instance name
  static T getWithName<T extends Object>(String instanceName) =>
      _getIt.get<T>(instanceName: instanceName);

  /// Check if a dependency is registered
  static bool isRegistered<T extends Object>({String? instanceName}) =>
      _getIt.isRegistered<T>(instanceName: instanceName);

  /// Register a singleton dependency
  static void registerSingleton<T extends Object>(T instance) =>
      _getIt.registerSingleton<T>(instance);

  /// Register a lazy singleton dependency
  static void registerLazySingleton<T extends Object>(T Function() factory) =>
      _getIt.registerLazySingleton<T>(factory);

  /// Register a factory dependency
  static void registerFactory<T extends Object>(T Function() factory) =>
      _getIt.registerFactory<T>(factory);

  /// Unregister a dependency
  static Future<void> unregister<T extends Object>({String? instanceName}) async {
    await _getIt.unregister<T>(instanceName: instanceName);
  }

  /// Reset all dependencies
  static Future<void> reset() => _getIt.reset();

  /// Get all registered dependencies info
  static String getAllRegisteredInfo() {
    final buffer = StringBuffer();
    buffer.writeln('Registered Dependencies:');

    // This is a simplified version - GetIt doesn't provide direct access to all registrations
    // In a real implementation, you might want to maintain your own registry
    buffer.writeln('- Logger');
    buffer.writeln('- Dio');
    buffer.writeln('- ErrorHandler');
    buffer.writeln('- NetworkInfo');
    buffer.writeln('- HttpClient');
    buffer.writeln('- LocalStorage');
    buffer.writeln('- SecureStorage');
    buffer.writeln('- CacheManager');

    return buffer.toString();
  }
}

/// Extension methods for easier access to common dependencies
extension ServiceLocatorExtensions on ServiceLocator {
  /// Quick access to logger
  static get logger => ServiceLocator.get<dynamic>(); // Will be typed properly after Logger setup

  /// Quick access to HTTP client
  static get httpClient => ServiceLocator.get<dynamic>(); // Will be typed properly after HttpClient setup

  /// Quick access to local storage
  static get localStorage => ServiceLocator.get<dynamic>(); // Will be typed properly after LocalStorage setup

  /// Quick access to secure storage
  static get secureStorage => ServiceLocator.get<dynamic>(); // Will be typed properly after SecureStorage setup

  /// Quick access to cache manager
  static get cacheManager => ServiceLocator.get<dynamic>(); // Will be typed properly after CacheManager setup
}
