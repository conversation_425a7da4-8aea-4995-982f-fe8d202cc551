import 'package:flutter_test/flutter_test.dart';

import 'package:ecommerce_app/core/utils/helpers/validation_helper.dart';

void main() {
  group('ValidationHelper', () {
    group('isValidEmail', () {
      test('should return true for valid email addresses', () {
        // Arrange & Act & Assert
        expect(ValidationHelper.isValidEmail('<EMAIL>'), isTrue);
        expect(ValidationHelper.isValidEmail('<EMAIL>'), isTrue);
        expect(ValidationHelper.isValidEmail('<EMAIL>'), isTrue);
        expect(ValidationHelper.isValidEmail('<EMAIL>'), isTrue);
      });

      test('should return false for invalid email addresses', () {
        // Arrange & Act & Assert
        expect(ValidationHelper.isValidEmail(''), isFalse);
        expect(ValidationHelper.isValidEmail('invalid'), isFalse);
        expect(ValidationHelper.isValidEmail('invalid@'), isFalse);
        expect(ValidationHelper.isValidEmail('@example.com'), isFalse);
        expect(ValidationHelper.isValidEmail('invalid@.com'), isFalse);
        expect(ValidationHelper.isValidEmail('invalid.example.com'), isFalse);
      });
    });

    group('isValidPhone', () {
      test('should return true for valid phone numbers', () {
        // Arrange & Act & Assert
        expect(ValidationHelper.isValidPhone('+1234567890'), isTrue);
        expect(ValidationHelper.isValidPhone('1234567890'), isTrue);
        expect(ValidationHelper.isValidPhone('+44123456789'), isTrue);
      });

      test('should return false for invalid phone numbers', () {
        // Arrange & Act & Assert
        expect(ValidationHelper.isValidPhone(''), isFalse);
        expect(ValidationHelper.isValidPhone('123'), isFalse);
        expect(ValidationHelper.isValidPhone('abc123'), isFalse);
        expect(ValidationHelper.isValidPhone('+'), isFalse);
      });
    });

    group('isValidPassword', () {
      test('should return true for valid passwords', () {
        // Arrange & Act & Assert
        expect(ValidationHelper.isValidPassword('Password123'), isTrue);
        expect(ValidationHelper.isValidPassword('MySecure123'), isTrue);
        expect(ValidationHelper.isValidPassword('Test1234'), isTrue);
      });

      test('should return false for invalid passwords', () {
        // Arrange & Act & Assert
        expect(ValidationHelper.isValidPassword(''), isFalse);
        expect(ValidationHelper.isValidPassword('short'), isFalse);
        expect(ValidationHelper.isValidPassword('password'), isFalse); // no uppercase or number
        expect(ValidationHelper.isValidPassword('PASSWORD'), isFalse); // no lowercase or number
        expect(ValidationHelper.isValidPassword('Password'), isFalse); // no number
        expect(ValidationHelper.isValidPassword('password123'), isFalse); // no uppercase
        expect(ValidationHelper.isValidPassword('PASSWORD123'), isFalse); // no lowercase
      });
    });

    group('isValidUsername', () {
      test('should return true for valid usernames', () {
        // Arrange & Act & Assert
        expect(ValidationHelper.isValidUsername('user123'), isTrue);
        expect(ValidationHelper.isValidUsername('test_user'), isTrue);
        expect(ValidationHelper.isValidUsername('User_Name_123'), isTrue);
      });

      test('should return false for invalid usernames', () {
        // Arrange & Act & Assert
        expect(ValidationHelper.isValidUsername(''), isFalse);
        expect(ValidationHelper.isValidUsername('us'), isFalse); // too short
        expect(ValidationHelper.isValidUsername('user-name'), isFalse); // contains dash
        expect(ValidationHelper.isValidUsername('user name'), isFalse); // contains space
        expect(ValidationHelper.isValidUsername('user@name'), isFalse); // contains special char
      });
    });

    group('validateRequired', () {
      test('should return null for non-empty values', () {
        // Arrange & Act & Assert
        expect(ValidationHelper.validateRequired('test'), isNull);
        expect(ValidationHelper.validateRequired('  test  '), isNull);
      });

      test('should return error message for empty values', () {
        // Arrange & Act & Assert
        expect(ValidationHelper.validateRequired(''), equals('Field is required'));
        expect(ValidationHelper.validateRequired('   '), equals('Field is required'));
        expect(ValidationHelper.validateRequired(null), equals('Field is required'));
      });

      test('should use custom field name in error message', () {
        // Arrange & Act & Assert
        expect(
          ValidationHelper.validateRequired('', fieldName: 'Email'),
          equals('Email is required'),
        );
      });
    });

    group('validateEmail', () {
      test('should return null for valid email', () {
        // Arrange & Act & Assert
        expect(ValidationHelper.validateEmail('<EMAIL>'), isNull);
      });

      test('should return error message for invalid email', () {
        // Arrange & Act & Assert
        expect(
          ValidationHelper.validateEmail('invalid'),
          equals('Please enter a valid email address'),
        );
        expect(
          ValidationHelper.validateEmail(''),
          equals('Email is required'),
        );
        expect(
          ValidationHelper.validateEmail(null),
          equals('Email is required'),
        );
      });
    });

    group('validatePassword', () {
      test('should return null for valid password', () {
        // Arrange & Act & Assert
        expect(ValidationHelper.validatePassword('Password123'), isNull);
      });

      test('should return appropriate error messages for invalid passwords', () {
        // Arrange & Act & Assert
        expect(
          ValidationHelper.validatePassword(''),
          equals('Password is required'),
        );
        expect(
          ValidationHelper.validatePassword(null),
          equals('Password is required'),
        );
        expect(
          ValidationHelper.validatePassword('short'),
          equals('Password must be at least 8 characters'),
        );
        expect(
          ValidationHelper.validatePassword('password'),
          equals('Password must contain at least one uppercase letter'),
        );
        expect(
          ValidationHelper.validatePassword('PASSWORD'),
          equals('Password must contain at least one lowercase letter'),
        );
        expect(
          ValidationHelper.validatePassword('Password'),
          equals('Password must contain at least one number'),
        );
      });
    });

    group('validateConfirmPassword', () {
      test('should return null when passwords match', () {
        // Arrange & Act & Assert
        expect(
          ValidationHelper.validateConfirmPassword('Password123', 'Password123'),
          isNull,
        );
      });

      test('should return error when passwords do not match', () {
        // Arrange & Act & Assert
        expect(
          ValidationHelper.validateConfirmPassword('Password123', 'Different123'),
          equals('Passwords do not match'),
        );
      });

      test('should return error when confirm password is empty', () {
        // Arrange & Act & Assert
        expect(
          ValidationHelper.validateConfirmPassword('', 'Password123'),
          equals('Please confirm your password'),
        );
        expect(
          ValidationHelper.validateConfirmPassword(null, 'Password123'),
          equals('Please confirm your password'),
        );
      });
    });

    group('validateNumeric', () {
      test('should return null for valid numbers', () {
        // Arrange & Act & Assert
        expect(ValidationHelper.validateNumeric('123'), isNull);
        expect(ValidationHelper.validateNumeric('123.45'), isNull);
        expect(ValidationHelper.validateNumeric('-123'), isNull);
        expect(ValidationHelper.validateNumeric('0'), isNull);
      });

      test('should return error for invalid numbers', () {
        // Arrange & Act & Assert
        expect(
          ValidationHelper.validateNumeric('abc'),
          equals('Field must be a valid number'),
        );
        expect(
          ValidationHelper.validateNumeric(''),
          equals('Field is required'),
        );
        expect(
          ValidationHelper.validateNumeric(null),
          equals('Field is required'),
        );
      });
    });

    group('validatePositiveNumber', () {
      test('should return null for positive numbers', () {
        // Arrange & Act & Assert
        expect(ValidationHelper.validatePositiveNumber('123'), isNull);
        expect(ValidationHelper.validatePositiveNumber('0.1'), isNull);
      });

      test('should return error for non-positive numbers', () {
        // Arrange & Act & Assert
        expect(
          ValidationHelper.validatePositiveNumber('0'),
          equals('Field must be greater than 0'),
        );
        expect(
          ValidationHelper.validatePositiveNumber('-123'),
          equals('Field must be greater than 0'),
        );
      });
    });

    group('validateUrl', () {
      test('should return null for valid URLs', () {
        // Arrange & Act & Assert
        expect(ValidationHelper.validateUrl('https://example.com'), isNull);
        expect(ValidationHelper.validateUrl('http://example.com'), isNull);
        expect(ValidationHelper.validateUrl('https://www.example.com/path'), isNull);
      });

      test('should return error for invalid URLs', () {
        // Arrange & Act & Assert
        expect(
          ValidationHelper.validateUrl('invalid'),
          equals('Please enter a valid URL'),
        );
        expect(
          ValidationHelper.validateUrl('ftp://example.com'),
          equals('Please enter a valid URL'),
        );
        expect(
          ValidationHelper.validateUrl(''),
          equals('URL is required'),
        );
      });
    });

    group('isValidCreditCard', () {
      test('should return true for valid credit card numbers', () {
        // Arrange & Act & Assert
        // Visa test number
        expect(ValidationHelper.isValidCreditCard('****************'), isTrue);
        // MasterCard test number
        expect(ValidationHelper.isValidCreditCard('****************'), isTrue);
        // With spaces
        expect(ValidationHelper.isValidCreditCard('4111 1111 1111 1111'), isTrue);
      });

      test('should return false for invalid credit card numbers', () {
        // Arrange & Act & Assert
        expect(ValidationHelper.isValidCreditCard(''), isFalse);
        expect(ValidationHelper.isValidCreditCard('123'), isFalse);
        expect(ValidationHelper.isValidCreditCard('1234567890123456'), isFalse);
        expect(ValidationHelper.isValidCreditCard('abcd1234efgh5678'), isFalse);
      });
    });
  });
}
