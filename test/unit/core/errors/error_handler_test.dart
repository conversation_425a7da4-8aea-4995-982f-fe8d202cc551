import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import 'package:mocktail/mocktail.dart';

import 'package:ecommerce_app/core/errors/error_handler.dart';
import 'package:ecommerce_app/core/errors/exceptions.dart';
import 'package:ecommerce_app/core/errors/failures.dart';

class MockLogger extends Mock implements Logger {}

void main() {
  group('ErrorHandlerImpl', () {
    late ErrorHandlerImpl errorHandler;
    late MockLogger mockLogger;

    setUp(() {
      mockLogger = MockLogger();
      errorHandler = ErrorHandlerImpl(logger: mockLogger);
    });

    group('handleException', () {
      test('should return NetworkFailure when NetworkException is passed', () {
        // Arrange
        const exception = NetworkException(
          message: 'Network error',
          code: 'NETWORK_ERROR',
        );

        // Act
        final result = errorHandler.handleException(exception);

        // Assert
        expect(result, isA<NetworkFailure>());
        expect(result.message, equals('Network error'));
        expect(result.code, equals('NETWORK_ERROR'));
        verify(() => mockLogger.e('Exception occurred: $exception')).called(1);
      });

      test('should return ServerFailure when ServerException is passed', () {
        // Arrange
        const exception = ServerException(
          message: 'Server error',
          code: '500',
        );

        // Act
        final result = errorHandler.handleException(exception);

        // Assert
        expect(result, isA<ServerFailure>());
        expect(result.message, equals('Server error'));
        expect(result.code, equals('500'));
      });

      test('should return CacheFailure when CacheException is passed', () {
        // Arrange
        const exception = CacheException(
          message: 'Cache error',
          code: 'CACHE_ERROR',
        );

        // Act
        final result = errorHandler.handleException(exception);

        // Assert
        expect(result, isA<CacheFailure>());
        expect(result.message, equals('Cache error'));
        expect(result.code, equals('CACHE_ERROR'));
      });

      test('should return AuthenticationFailure when AuthenticationException is passed', () {
        // Arrange
        const exception = AuthenticationException(
          message: 'Authentication error',
          code: 'AUTH_ERROR',
        );

        // Act
        final result = errorHandler.handleException(exception);

        // Assert
        expect(result, isA<AuthenticationFailure>());
        expect(result.message, equals('Authentication error'));
        expect(result.code, equals('AUTH_ERROR'));
      });

      test('should return ValidationFailure when ValidationException is passed', () {
        // Arrange
        const exception = ValidationException(
          message: 'Validation error',
          code: 'VALIDATION_ERROR',
        );

        // Act
        final result = errorHandler.handleException(exception);

        // Assert
        expect(result, isA<ValidationFailure>());
        expect(result.message, equals('Validation error'));
        expect(result.code, equals('VALIDATION_ERROR'));
      });

      test('should return PlatformFailure when PlatformException is passed', () {
        // Arrange
        const exception = PlatformException(
          message: 'Platform error',
          code: 'PLATFORM_ERROR',
        );

        // Act
        final result = errorHandler.handleException(exception);

        // Assert
        expect(result, isA<PlatformFailure>());
        expect(result.message, equals('Platform error'));
        expect(result.code, equals('PLATFORM_ERROR'));
      });

      test('should return UnknownFailure when unknown exception is passed', () {
        // Arrange
        final exception = Exception('Unknown error');

        // Act
        final result = errorHandler.handleException(exception);

        // Assert
        expect(result, isA<UnknownFailure>());
        expect(result.message, equals('Exception: Unknown error'));
      });
    });

    group('handleDioError', () {
      test('should return NetworkFailure for connection timeout', () {
        // Arrange
        final dioError = DioException(
          type: DioExceptionType.connectionTimeout,
          requestOptions: RequestOptions(path: '/test'),
          message: 'Connection timeout',
        );

        // Act
        final result = errorHandler.handleDioError(dioError);

        // Assert
        expect(result, isA<NetworkFailure>());
        expect(result.message, contains('Connection timeout'));
        expect(result.code, equals('TIMEOUT'));
      });

      test('should return NetworkFailure for send timeout', () {
        // Arrange
        final dioError = DioException(
          type: DioExceptionType.sendTimeout,
          requestOptions: RequestOptions(path: '/test'),
        );

        // Act
        final result = errorHandler.handleDioError(dioError);

        // Assert
        expect(result, isA<NetworkFailure>());
        expect(result.code, equals('TIMEOUT'));
      });

      test('should return NetworkFailure for receive timeout', () {
        // Arrange
        final dioError = DioException(
          type: DioExceptionType.receiveTimeout,
          requestOptions: RequestOptions(path: '/test'),
        );

        // Act
        final result = errorHandler.handleDioError(dioError);

        // Assert
        expect(result, isA<NetworkFailure>());
        expect(result.code, equals('TIMEOUT'));
      });

      test('should return ServerFailure for bad response with 400 status', () {
        // Arrange
        final dioError = DioException(
          type: DioExceptionType.badResponse,
          requestOptions: RequestOptions(path: '/test'),
          response: Response(
            statusCode: 400,
            requestOptions: RequestOptions(path: '/test'),
          ),
        );

        // Act
        final result = errorHandler.handleDioError(dioError);

        // Assert
        expect(result, isA<ServerFailure>());
        expect(result.message, contains('Bad request'));
        expect(result.code, equals('400'));
      });

      test('should return ServerFailure for bad response with 401 status', () {
        // Arrange
        final dioError = DioException(
          type: DioExceptionType.badResponse,
          requestOptions: RequestOptions(path: '/test'),
          response: Response(
            statusCode: 401,
            requestOptions: RequestOptions(path: '/test'),
          ),
        );

        // Act
        final result = errorHandler.handleDioError(dioError);

        // Assert
        expect(result, isA<ServerFailure>());
        expect(result.message, contains('Unauthorized'));
        expect(result.code, equals('401'));
      });

      test('should return ServerFailure for bad response with 500 status', () {
        // Arrange
        final dioError = DioException(
          type: DioExceptionType.badResponse,
          requestOptions: RequestOptions(path: '/test'),
          response: Response(
            statusCode: 500,
            requestOptions: RequestOptions(path: '/test'),
          ),
        );

        // Act
        final result = errorHandler.handleDioError(dioError);

        // Assert
        expect(result, isA<ServerFailure>());
        expect(result.message, contains('Internal server error'));
        expect(result.code, equals('500'));
      });

      test('should return NetworkFailure for cancel', () {
        // Arrange
        final dioError = DioException(
          type: DioExceptionType.cancel,
          requestOptions: RequestOptions(path: '/test'),
        );

        // Act
        final result = errorHandler.handleDioError(dioError);

        // Assert
        expect(result, isA<NetworkFailure>());
        expect(result.message, contains('cancelled'));
        expect(result.code, equals('CANCELLED'));
      });

      test('should return NetworkFailure for connection error', () {
        // Arrange
        final dioError = DioException(
          type: DioExceptionType.connectionError,
          requestOptions: RequestOptions(path: '/test'),
        );

        // Act
        final result = errorHandler.handleDioError(dioError);

        // Assert
        expect(result, isA<NetworkFailure>());
        expect(result.message, contains('No internet connection'));
        expect(result.code, equals('NO_CONNECTION'));
      });

      test('should return NetworkFailure for bad certificate', () {
        // Arrange
        final dioError = DioException(
          type: DioExceptionType.badCertificate,
          requestOptions: RequestOptions(path: '/test'),
        );

        // Act
        final result = errorHandler.handleDioError(dioError);

        // Assert
        expect(result, isA<NetworkFailure>());
        expect(result.message, contains('Certificate verification failed'));
        expect(result.code, equals('BAD_CERTIFICATE'));
      });

      test('should return NetworkFailure for unknown error', () {
        // Arrange
        final dioError = DioException(
          type: DioExceptionType.unknown,
          requestOptions: RequestOptions(path: '/test'),
          message: 'Unknown error',
        );

        // Act
        final result = errorHandler.handleDioError(dioError);

        // Assert
        expect(result, isA<NetworkFailure>());
        expect(result.message, contains('Unknown error'));
        expect(result.code, equals('UNKNOWN'));
      });
    });

    group('logError', () {
      test('should log error with logger', () {
        // Arrange
        const error = 'Test error';
        final stackTrace = StackTrace.current;

        // Act
        errorHandler.logError(error, stackTrace);

        // Assert
        verify(() => mockLogger.e(
          'Error occurred: $error',
          error: error,
          stackTrace: stackTrace,
        )).called(1);
      });
    });
  });
}
