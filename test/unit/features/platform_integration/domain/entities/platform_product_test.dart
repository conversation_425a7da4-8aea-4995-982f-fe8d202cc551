import 'package:flutter_test/flutter_test.dart';
import 'package:ecommerce_app/features/platform_integration/domain/entities/platform_product.dart';

void main() {
  group('PlatformProduct', () {
    late PlatformProduct product;
    late DateTime now;

    setUp(() {
      now = DateTime.now();
      product = PlatformProduct(
        id: '1',
        title: 'Test Product',
        description: 'Test Description',
        price: 99.99,
        currency: 'USD',
        images: const [
          PlatformProductImage(
            id: '1',
            url: 'https://example.com/image.jpg',
            altText: 'Test Image',
          ),
        ],
        variants: const [
          PlatformProductVariant(
            id: '1',
            title: 'Default',
            price: 99.99,
            isAvailable: true,
            inventoryQuantity: 10,
          ),
        ],
        isAvailable: true,
        createdAt: now,
        updatedAt: now,
        compareAtPrice: 129.99,
        vendor: 'Test Vendor',
        productType: 'Test Type',
        tags: const ['test', 'product'],
        handle: 'test-product',
      );
    });

    test('should return main image when images are available', () {
      expect(product.mainImage, isNotNull);
      expect(product.mainImage!.id, '1');
    });

    test('should return null for main image when no images', () {
      final productWithoutImages = PlatformProduct(
        id: product.id,
        title: product.title,
        description: product.description,
        price: product.price,
        currency: product.currency,
        images: const [],
        variants: product.variants,
        isAvailable: product.isAvailable,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
      );
      expect(productWithoutImages.mainImage, isNull);
    });

    test('should correctly identify if product is on sale', () {
      expect(product.isOnSale, isTrue);

      final productNotOnSale = PlatformProduct(
        id: product.id,
        title: product.title,
        description: product.description,
        price: product.price,
        currency: product.currency,
        images: product.images,
        variants: product.variants,
        isAvailable: product.isAvailable,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
        compareAtPrice: null,
      );
      expect(productNotOnSale.isOnSale, isFalse);
    });

    test('should calculate discount percentage correctly', () {
      expect(product.discountPercentage, closeTo(23.08, 0.01));

      final productNotOnSale = product.copyWith(compareAtPrice: null);
      expect(productNotOnSale.discountPercentage, 0.0);
    });

    test('should calculate price range for variants', () {
      final productWithVariants = product.copyWith(
        variants: const [
          PlatformProductVariant(
            id: '1',
            title: 'Variant 1',
            price: 50.0,
            isAvailable: true,
            inventoryQuantity: 10,
          ),
          PlatformProductVariant(
            id: '2',
            title: 'Variant 2',
            price: 100.0,
            isAvailable: true,
            inventoryQuantity: 5,
          ),
        ],
      );

      final priceRange = productWithVariants.priceRange;
      expect(priceRange.min, 50.0);
      expect(priceRange.max, 100.0);
      expect(priceRange.isSinglePrice, isFalse);
    });

    test('should return single price when no variants', () {
      final productWithoutVariants = product.copyWith(variants: []);
      final priceRange = productWithoutVariants.priceRange;

      expect(priceRange.min, product.price);
      expect(priceRange.max, product.price);
      expect(priceRange.isSinglePrice, isTrue);
    });

    test('should support equality comparison', () {
      final product1 = PlatformProduct(
        id: '1',
        title: 'Test',
        description: 'Test',
        price: 10.0,
        currency: 'USD',
        images: const [],
        variants: const [],
        isAvailable: true,
        createdAt: now,
        updatedAt: now,
      );

      final product2 = PlatformProduct(
        id: '1',
        title: 'Test',
        description: 'Test',
        price: 10.0,
        currency: 'USD',
        images: const [],
        variants: const [],
        isAvailable: true,
        createdAt: now,
        updatedAt: now,
      );

      expect(product1, equals(product2));
    });
  });

  group('PlatformProductVariant', () {
    test('should correctly identify if variant is on sale', () {
      const variant = PlatformProductVariant(
        id: '1',
        title: 'Test Variant',
        price: 50.0,
        isAvailable: true,
        inventoryQuantity: 10,
        compareAtPrice: 70.0,
      );

      expect(variant.isOnSale, isTrue);

      const variantNotOnSale = PlatformProductVariant(
        id: '1',
        title: 'Test Variant',
        price: 50.0,
        isAvailable: true,
        inventoryQuantity: 10,
      );

      expect(variantNotOnSale.isOnSale, isFalse);
    });
  });

  group('PriceRange', () {
    test('should correctly identify single price', () {
      const singlePrice = PriceRange(min: 50.0, max: 50.0);
      expect(singlePrice.isSinglePrice, isTrue);

      const rangePrice = PriceRange(min: 50.0, max: 100.0);
      expect(rangePrice.isSinglePrice, isFalse);
    });
  });
}
