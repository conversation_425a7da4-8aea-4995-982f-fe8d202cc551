name: ecommerce_app
description: "A scalable Flutter ecommerce application with multi-platform integration"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.0
  flutter: ">=3.24.0"

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_bloc: ^8.1.6
  bloc: ^8.1.4
  equatable: ^2.0.5

  # Dependency Injection
  get_it: ^8.0.0
  injectable: ^2.4.4

  # Network
  dio: ^5.7.0
  retrofit: ^4.4.1
  json_annotation: ^4.9.0

  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.3.2
  flutter_secure_storage: ^9.2.2

  # Functional Programming
  dartz: ^0.10.1

  # UI & Navigation
  go_router: ^14.6.1
  cached_network_image: ^3.4.1
  flutter_svg: ^2.0.10+1
  shimmer: ^3.0.0

  # Utilities
  intl: ^0.19.0
  logger: ^2.4.0
  uuid: ^4.5.1

  # Platform Integration
  webview_flutter: ^4.9.0
  url_launcher: ^6.3.1

  cupertino_icons: ^1.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.13
  injectable_generator: ^2.6.2
  retrofit_generator: ^9.1.2
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1

  # Testing
  bloc_test: ^9.1.7
  mocktail: ^1.0.4
  integration_test:
    sdk: flutter

  # Linting
  flutter_lints: ^5.0.0
  very_good_analysis: ^6.0.0

flutter:
  uses-material-design: true

  # assets:
  #   - assets/images/
  #   - assets/icons/
  #   - material-theme.json

  # fonts:
  #   - family: Inter
  #     fonts:
  #       - asset: assets/fonts/Inter-Regular.ttf
  #       - asset: assets/fonts/Inter-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Inter-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Inter-Bold.ttf
  #         weight: 700
