name: meditaguru
description: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> Tỉnh

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev
homepage: https://thienthuctinh.com/

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 2.4.2+111

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.5
  flutter_svg: 2.0.7
  rxdart: ^0.27.7
  flutter_cache_manager: ^3.4.1
  flutter_html: 3.0.0-beta.2
  vector_math: ^2.1.4
  percent_indicator: ^4.2.2
  simple_animations: ^5.0.0+3
  connectivity_plus: ^6.1.3
  google_sign_in: ^6.1.3
  image_picker: ^0.8.6
  shimmer: ^3.0.0
  image_cropper: 9.1.0
  shared_preferences: ^2.0.15
  rflutter_alert: ^2.0.4
  in_app_purchase: ^3.0.8
  oauth1: ^2.0.0
  flutter_local_notifications: 18.0.1
  provider: ^6.0.5
  get_it: ^7.2.0
  after_layout: ^1.2.0
  flare_splash_screen: ^4.0.0
  localstorage: ^4.0.0+1
  pin_code_fields: ^7.4.0
  font_awesome_flutter: ^10.3.0
  extended_image: 8.1.1
  logging: ^1.1.0
  universal_platform: ^1.0.0+1
  transparent_image: ^2.0.0
  page_indicator: ^0.4.1
  flutter_spinkit: ^5.1.0
  pull_to_refresh: ^2.0.0
  retrofit: ^4.0.1
  timeago: ^3.3.0
  url_launcher: ^6.0.3
  package_info_plus: ^8.3.0
  device_info_plus: 11.3.3
  #  flutter_email_sender: ^5.1.0
  flutter_email_sender: 7.0.0
  share_plus: 11.0.0
  video_player: 2.9.5
  app_settings: ^5.0.0
  webview_flutter: 4.11.0
  audio_service: 0.18.17
#  audio_service:
#    git:
#      url: https://github.com/mypower119/audio_service.git
#      ref: minor
#      path: audio_service/

  # just_audio: ^0.9.40
  just_audio:
    git:
      url: https://github.com/xuannghia/just_audio.git
      ref: 6d5752b576052d11fcd05e51bc45b9b3eeef7f03
      path: just_audio/
    
  flutter_native_timezone_latest: ^1.0.0
  auto_size_text: ^3.0.0
  sign_in_with_apple: 7.0.1
  log_webhook: ^0.0.8
  firebase_storage: 12.2.0
  firebase_messaging: 15.1.0

  firebase_crashlytics: 4.1.0
  flutter_facebook_auth: 7.1.0

  firebase_core: 3.11.0
  firebase_analytics: 11.4.2
  firebase_auth: 5.4.2
  firebase_remote_config: 5.4.0
  firebase_dynamic_links: 6.1.2
  cloud_firestore: 5.6.3
  firebase_remote_config_web: 1.8.0

  chewie: ^1.10.0
  flutter_math_fork: ^0.7.1

  logger: ^2.4.0
  flutter_uxcam: 2.6.0
  smooth_page_indicator: ^1.1.0
  simple_gradient_text: ^1.3.0
#  flutter_twitter_login: ^1.1.0

  app_developer:
    path: modules/app_developer
  flutter_sticky_header: 0.7.0
  search_highlight_text: ^1.0.0+2
  lottie: ^3.1.0
  intl: ^0.19.0
  freezed_annotation: ^2.4.1
  scroll_to_index: ^3.0.1
  top_snackbar_flutter: ^2.1.1 # Toast message
  bot_toast: ^4.1.3
  visibility_detector: 0.4.0+2
  carousel_slider_plus: ^7.0.0
  collection: ^1.18.0
  csv: ^6.0.0
  wakelock_plus: 1.2.8
  cached_network_image: ^3.4.0
  permission_handler: ^11.4.0
  rate_my_app: ^2.3.1
  in_app_update: ^4.2.3
  new_version_plus: ^0.1.0

dependency_overrides:
  http: ^0.13.5

dev_dependencies:
#  intl_translation: ^0.17.1
#  flutter_test:
#    sdk: flutter
  build_runner: ^2.0.3
  flutter_launcher_icons: ^0.13.1

  retrofit_generator:
  json_serializable:
  freezed:
  flutter_test:
    sdk: flutter

flutter_icons:
  android: "ic_launcher"  # ic_launcher | ic_launcher_round
  ios: true
  image_path_ios: "assets/img/ic_launcher.png"
  image_path: "assets/img/ic_launcher.png" # ic_launcher.png | ic_launcher_round.png

flutter:

  uses-material-design: true
  assets:
    - assets/
    - assets/img/
    - assets/img/social-icon/
    - assets/img/bg/
    - assets/img/country/
    - assets/config/
    - lib/src/core/config/
    - assets/icons/
    - assets/media/
  fonts:
    - family: MyFont
      fonts:
        - asset: assets/font/BarlowSemiCondense/BarlowSemiCondensed-Regular.ttf
          weight: 400
        - asset: assets/font/BarlowSemiCondense/BarlowSemiCondensed-Bold.ttf
          weight: 700
        - asset: assets/font/BarlowSemiCondense/BarlowSemiCondensed-BoldItalic.ttf
          weight: 700
          style: italic
    - family: MyFont2
      fonts:
        - asset: assets/font/DancingScript/DancingScript-VariableFont_wght.ttf
    - family: MyFont3
      fonts:
        - asset: assets/font/SFPro/SF-Pro-Display-Regular.otf
          weight: 400
        - asset: assets/font/SFPro/SF-Pro-Display-Semibold.otf
          weight: 500
flutter_intl:
  enabled: true