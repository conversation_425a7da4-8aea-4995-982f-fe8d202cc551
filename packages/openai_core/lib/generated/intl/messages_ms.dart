// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ms locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ms';

  static String m0(limit) =>
      "Hanya terdapat ${limit}x carian imej dalam versi percuma.";

  static String m1(limit) =>
      "Sehingga ${limit} mesej hanya boleh dipaparkan dalam versi percuma.";

  static String m2(date) => "Tarikh tamat tempoh langganan ${date}";

  static String m3(number) =>
      "Hasilkan (${number} ${Intl.plural(number, one: 'time', other: 'times')})";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "about_openai": MessageLookupByLibrary.simpleMessage("Mengenai"),
    "apply_openai": MessageLookupByLibrary.simpleMessage("Sapukan"),
    "artist_openai": MessageLookupByLibrary.simpleMessage("Artis"),
    "cancel_openai": MessageLookupByLibrary.simpleMessage("Batalkan"),
    "chatDetail_openai": MessageLookupByLibrary.simpleMessage(
      "Butiran Sembang",
    ),
    "chatGPT_openai": MessageLookupByLibrary.simpleMessage("Sembang GPT"),
    "chatWithBot_openai": MessageLookupByLibrary.simpleMessage(
      "Berbual dengan Bot",
    ),
    "chat_openai": MessageLookupByLibrary.simpleMessage("Sembang"),
    "chooseArtist_openai": MessageLookupByLibrary.simpleMessage(
      "Pilih artis untuk imej anda",
    ),
    "chooseDetail_openai": MessageLookupByLibrary.simpleMessage(
      "Pilih butiran untuk imej anda",
    ),
    "chooseMedium_openai": MessageLookupByLibrary.simpleMessage(
      "Pilih medium untuk imej anda",
    ),
    "chooseMood_openai": MessageLookupByLibrary.simpleMessage(
      "Pilih mood untuk imej anda",
    ),
    "chooseUseCase_openai": MessageLookupByLibrary.simpleMessage(
      "Pilih kes penggunaan",
    ),
    "choseStyle_openai": MessageLookupByLibrary.simpleMessage(
      "Pilih gaya untuk imej anda",
    ),
    "clearConfirm_openai": MessageLookupByLibrary.simpleMessage(
      "Adakah anda pasti mengosongkan kandungan?",
    ),
    "clearContent_openai": MessageLookupByLibrary.simpleMessage(
      "Kandungan yang jelas",
    ),
    "clearConversation_openai": MessageLookupByLibrary.simpleMessage(
      "Perbualan yang jelas",
    ),
    "clear_openai": MessageLookupByLibrary.simpleMessage("Jelas"),
    "confirmDeleteItem_openai": MessageLookupByLibrary.simpleMessage(
      "Adakah anda pasti mahu memadamkan item ini?",
    ),
    "confirmDelete_openai": MessageLookupByLibrary.simpleMessage(
      "Sila sahkan jika anda ingin meneruskan pemadaman item ini. Anda tidak boleh membuat asal tindakan ini.",
    ),
    "confirmRemoveKey_openai": MessageLookupByLibrary.simpleMessage(
      "Adakah anda pasti akan mengalih keluar kunci?",
    ),
    "confirm_openai": MessageLookupByLibrary.simpleMessage("Sahkan"),
    "copiedToClipboard_openai": MessageLookupByLibrary.simpleMessage(
      "Menyalin kandungan ke papan keratan",
    ),
    "copy_openai": MessageLookupByLibrary.simpleMessage("Salinan"),
    "createChatFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Buat Sembang Gagal",
    ),
    "deleteFailed_openai": MessageLookupByLibrary.simpleMessage("Padam Gagal"),
    "delete_openai": MessageLookupByLibrary.simpleMessage("Padam"),
    "detail_openai": MessageLookupByLibrary.simpleMessage("Terperinci"),
    "download_openai": MessageLookupByLibrary.simpleMessage("Muat turun"),
    "edit_openai": MessageLookupByLibrary.simpleMessage("Edit"),
    "failedToGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "Gagal menjana",
    ),
    "generate_openai": MessageLookupByLibrary.simpleMessage("Menjana"),
    "grid_openai": MessageLookupByLibrary.simpleMessage("Grid"),
    "imageGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "Menjana imej",
    ),
    "imageSize_openai": MessageLookupByLibrary.simpleMessage("Saiz gambar"),
    "inputKey_openai": MessageLookupByLibrary.simpleMessage("Kunci Input"),
    "interest_openai": MessageLookupByLibrary.simpleMessage("minat"),
    "introAboutKey_openai": MessageLookupByLibrary.simpleMessage(
      "Kunci API anda disimpan secara setempat pada mudah alih anda dan tidak pernah dihantar ke tempat lain. Anda boleh menyimpan kunci anda untuk menggunakannya kemudian. Anda juga boleh mengalih keluar kunci anda jika anda tidak mahu menggunakannya lagi.",
    ),
    "invalidKey_openai": MessageLookupByLibrary.simpleMessage(
      "Kunci Tidak Sah",
    ),
    "jobRole_openai": MessageLookupByLibrary.simpleMessage("Peranan Kerja"),
    "jobSkills_openai": MessageLookupByLibrary.simpleMessage("Kemahiran kerja"),
    "layoutStyle_openai": MessageLookupByLibrary.simpleMessage(
      "Gaya Reka Letak",
    ),
    "limitImage_openai": m0,
    "limitTheText_openai": m1,
    "listening_openai": MessageLookupByLibrary.simpleMessage("Mendengar..."),
    "loadKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Kunci Muatan Gagal",
    ),
    "loadKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "Muatkan Kejayaan Utama",
    ),
    "manage_openai": MessageLookupByLibrary.simpleMessage("Mengurus"),
    "medium_openai": MessageLookupByLibrary.simpleMessage("Sederhana"),
    "mood_openai": MessageLookupByLibrary.simpleMessage("Mood"),
    "moreOptions_openai": MessageLookupByLibrary.simpleMessage(
      "Lebih banyak pilihan",
    ),
    "newChat_openai": MessageLookupByLibrary.simpleMessage("Sembang Baharu"),
    "noImageGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "Tiada imej yang dihasilkan",
    ),
    "numberOfImagesCondition_openai": MessageLookupByLibrary.simpleMessage(
      "Bilangan imej untuk dijana. Mesti antara 1 dan 10.",
    ),
    "numberOfImages_openai": MessageLookupByLibrary.simpleMessage(
      "Bilangan imej",
    ),
    "options_openai": MessageLookupByLibrary.simpleMessage("Pilihan"),
    "page_openai": MessageLookupByLibrary.simpleMessage("halaman"),
    "pleaseCheckConnection_openai": MessageLookupByLibrary.simpleMessage(
      "Sila semak sambungan anda dan cuba lagi!",
    ),
    "pleaseInputFillAllFields_openai": MessageLookupByLibrary.simpleMessage(
      "Sila isi semua ruangan",
    ),
    "pleaseInputKey_openai": MessageLookupByLibrary.simpleMessage(
      "Sila masukkan kunci",
    ),
    "prompt_openai": MessageLookupByLibrary.simpleMessage("Segera"),
    "putKeyHere_openai": MessageLookupByLibrary.simpleMessage(
      "Letakkan kunci anda di sini",
    ),
    "regenerateResponse_openai": MessageLookupByLibrary.simpleMessage(
      "Menjana semula tindak balas",
    ),
    "remaining_openai": MessageLookupByLibrary.simpleMessage("yang tinggal"),
    "removeKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Alih Keluar Kunci Gagal",
    ),
    "removeKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "Kunci Berjaya Dialih Keluar",
    ),
    "remove_openai": MessageLookupByLibrary.simpleMessage("Keluarkan"),
    "resetSettings_openai": MessageLookupByLibrary.simpleMessage(
      "Aturan semula",
    ),
    "reset_openai": MessageLookupByLibrary.simpleMessage("Tetapkan semula"),
    "saveKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Simpan Kunci Gagal",
    ),
    "saveKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "Berjaya Disimpan Kunci",
    ),
    "saveKey_openai": MessageLookupByLibrary.simpleMessage("Simpan Kunci"),
    "save_openai": MessageLookupByLibrary.simpleMessage("Simpan"),
    "searchByPrompt_openai": MessageLookupByLibrary.simpleMessage(
      "Cari mengikut Prompt...",
    ),
    "sectionKeywords_openai": MessageLookupByLibrary.simpleMessage(
      "Kata Kunci Bahagian",
    ),
    "sectionTopic_openai": MessageLookupByLibrary.simpleMessage(
      "Topik Bahagian",
    ),
    "selectChatFailed_openai": MessageLookupByLibrary.simpleMessage(
      "Pilih Sembang Gagal",
    ),
    "selectPrompt_openai": MessageLookupByLibrary.simpleMessage("Pilih Prompt"),
    "settings_openai": MessageLookupByLibrary.simpleMessage("Tetapan"),
    "share_openai": MessageLookupByLibrary.simpleMessage("Kongsi"),
    "skills_openai": MessageLookupByLibrary.simpleMessage("Kemahiran"),
    "somethingWentWrong_openai": MessageLookupByLibrary.simpleMessage(
      "Sesuatu telah berlaku!!!",
    ),
    "somethingWhenWrong_openai": MessageLookupByLibrary.simpleMessage(
      "Sesuatu telah berlaku! Sila cuba sebentar lagi. Terima kasih banyak-banyak!",
    ),
    "speechNotAvailable_openai": MessageLookupByLibrary.simpleMessage(
      "Ucapan tidak tersedia",
    ),
    "style_openai": MessageLookupByLibrary.simpleMessage("Gaya"),
    "subscriptionExpiredDate_openai": m2,
    "tapTheMicToTalk_openai": MessageLookupByLibrary.simpleMessage(
      "Ketik mikrofon untuk bercakap",
    ),
    "textGenerate_openai": MessageLookupByLibrary.simpleMessage("Menjana teks"),
    "textGenerator_openai": MessageLookupByLibrary.simpleMessage(
      "Penjana Teks",
    ),
    "timeGenerate_openai": m3,
    "typeAMessage_openai": MessageLookupByLibrary.simpleMessage(
      "Taip mesej...",
    ),
    "viewType_openai": MessageLookupByLibrary.simpleMessage("Jenis paparan"),
    "view_openai": MessageLookupByLibrary.simpleMessage("Lihat"),
    "write_openai": MessageLookupByLibrary.simpleMessage("tulis"),
  };
}
