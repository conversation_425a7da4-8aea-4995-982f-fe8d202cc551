// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a ur locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ur';

  static String m0(limit) => "مفت ورژن میں صرف ${limit}x تصویر کی تلاش ہے۔";

  static String m1(limit) =>
      " ${limit} پیغامات تک صرف مفت ورژن میں دکھائے جا سکتے ہیں۔";

  static String m2(date) => "سبسکرپشن کی میعاد ختم ہونے کی تاریخ ${date}";

  static String m3(number) =>
      "بنائیں (${number} ${Intl.plural(number, one: 'time', other: 'times')})";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about_openai": MessageLookupByLibrary.simpleMessage("کے بارے میں"),
        "apply_openai": MessageLookupByLibrary.simpleMessage("درخواست دیں"),
        "artist_openai": MessageLookupByLibrary.simpleMessage("فنکار"),
        "cancel_openai": MessageLookupByLibrary.simpleMessage("منسوخ کریں"),
        "chatDetail_openai":
            MessageLookupByLibrary.simpleMessage("چیٹ کی تفصیل"),
        "chatGPT_openai": MessageLookupByLibrary.simpleMessage("GPT چیٹ کریں۔"),
        "chatWithBot_openai":
            MessageLookupByLibrary.simpleMessage("بوٹ کے ساتھ چیٹ کریں۔"),
        "chat_openai": MessageLookupByLibrary.simpleMessage("چیٹ"),
        "chooseArtist_openai": MessageLookupByLibrary.simpleMessage(
            "اپنی تصویر کے لیے فنکار کا انتخاب کریں۔"),
        "chooseDetail_openai": MessageLookupByLibrary.simpleMessage(
            "اپنی تصویر کے لیے تفصیل کا انتخاب کریں۔"),
        "chooseMedium_openai": MessageLookupByLibrary.simpleMessage(
            "اپنی تصویر کے لیے میڈیم کا انتخاب کریں۔"),
        "chooseMood_openai": MessageLookupByLibrary.simpleMessage(
            "اپنی تصویر کے لیے موڈ کا انتخاب کریں۔"),
        "chooseUseCase_openai":
            MessageLookupByLibrary.simpleMessage("استعمال کیس کا انتخاب کریں۔"),
        "choseStyle_openai": MessageLookupByLibrary.simpleMessage(
            "اپنی تصویر کے لیے اسٹائل کا انتخاب کریں۔"),
        "clearConfirm_openai": MessageLookupByLibrary.simpleMessage(
            "کیا آپ مواد کو صاف کرنے کا یقین رکھتے ہیں؟"),
        "clearContent_openai":
            MessageLookupByLibrary.simpleMessage("مواد صاف کریں۔"),
        "clearConversation_openai":
            MessageLookupByLibrary.simpleMessage("بات چیت صاف کریں۔"),
        "clear_openai": MessageLookupByLibrary.simpleMessage("صاف"),
        "confirmDeleteItem_openai": MessageLookupByLibrary.simpleMessage(
            "کیا آپ واقعی اس آئٹم کو حذف کرنا چاہتے ہیں؟"),
        "confirmDelete_openai": MessageLookupByLibrary.simpleMessage(
            "اگر آپ اس آئٹم کو حذف کرنے کے ساتھ آگے بڑھنا چاہتے ہیں تو براہ کرم تصدیق کریں۔ آپ اس کارروائی کو کالعدم نہیں کر سکتے۔"),
        "confirmRemoveKey_openai": MessageLookupByLibrary.simpleMessage(
            "کیا آپ یقینی طور پر کلید کو ہٹانا چاہتے ہیں؟"),
        "confirm_openai": MessageLookupByLibrary.simpleMessage("تصدیق کریں"),
        "copiedToClipboard_openai": MessageLookupByLibrary.simpleMessage(
            "مواد کو کلپ بورڈ پر کاپی کیا گیا۔"),
        "copy_openai": MessageLookupByLibrary.simpleMessage("کاپی"),
        "createChatFailed_openai":
            MessageLookupByLibrary.simpleMessage("چیٹ بنانا ناکام ہو گیا۔"),
        "deleteFailed_openai":
            MessageLookupByLibrary.simpleMessage("حذف کرنا ناکام ہو گیا۔"),
        "delete_openai": MessageLookupByLibrary.simpleMessage("حذف کریں"),
        "detail_openai": MessageLookupByLibrary.simpleMessage("تفصیل"),
        "download_openai":
            MessageLookupByLibrary.simpleMessage("ڈاؤن لوڈ کریں"),
        "edit_openai": MessageLookupByLibrary.simpleMessage("ترمیم"),
        "failedToGenerate_openai":
            MessageLookupByLibrary.simpleMessage("پیدا کرنے میں ناکام"),
        "generate_openai": MessageLookupByLibrary.simpleMessage("پیدا کرنا"),
        "grid_openai": MessageLookupByLibrary.simpleMessage("گرڈ"),
        "imageGenerate_openai":
            MessageLookupByLibrary.simpleMessage("تصویر بنائیں"),
        "imageSize_openai":
            MessageLookupByLibrary.simpleMessage("تصویر کا سائز"),
        "inputKey_openai": MessageLookupByLibrary.simpleMessage("ان پٹ کلید"),
        "interest_openai": MessageLookupByLibrary.simpleMessage("دلچسپی"),
        "introAboutKey_openai": MessageLookupByLibrary.simpleMessage(
            "آپ کی API کلید مقامی طور پر آپ کے موبائل پر محفوظ ہوتی ہے اور کبھی بھی کہیں اور نہیں بھیجی جاتی۔ آپ اپنی کلید کو بعد میں استعمال کرنے کے لیے محفوظ کر سکتے ہیں۔ اگر آپ اسے مزید استعمال نہیں کرنا چاہتے ہیں تو آپ اسے ہٹا بھی سکتے ہیں۔"),
        "invalidKey_openai": MessageLookupByLibrary.simpleMessage("غلط کلید"),
        "jobRole_openai":
            MessageLookupByLibrary.simpleMessage("عملی ذمہ داری، عملی کردار"),
        "jobSkills_openai":
            MessageLookupByLibrary.simpleMessage("ملازمت کی مہارتیں"),
        "layoutStyle_openai":
            MessageLookupByLibrary.simpleMessage("ترتیب کا طریقا"),
        "limitImage_openai": m0,
        "limitTheText_openai": m1,
        "listening_openai":
            MessageLookupByLibrary.simpleMessage("سن رہا ہے..."),
        "loadKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("لوڈ کلید ناکام ہو گئی۔"),
        "loadKeySuccess_openai":
            MessageLookupByLibrary.simpleMessage("لوڈ کلیدی کامیابی"),
        "manage_openai": MessageLookupByLibrary.simpleMessage("انتظام کریں"),
        "medium_openai": MessageLookupByLibrary.simpleMessage("میڈیم"),
        "mood_openai": MessageLookupByLibrary.simpleMessage("مزاج"),
        "moreOptions_openai":
            MessageLookupByLibrary.simpleMessage("مزید زرائے"),
        "newChat_openai": MessageLookupByLibrary.simpleMessage("نئی چیٹ"),
        "noImageGenerate_openai":
            MessageLookupByLibrary.simpleMessage("کوئی تصویر نہیں بنتی"),
        "numberOfImagesCondition_openai": MessageLookupByLibrary.simpleMessage(
            "تخلیق کرنے والی تصاویر کی تعداد۔ 1 اور 10 کے درمیان ہونا چاہیے۔"),
        "numberOfImages_openai":
            MessageLookupByLibrary.simpleMessage("تصاویر کی تعداد"),
        "options_openai": MessageLookupByLibrary.simpleMessage("اختیارات"),
        "page_openai": MessageLookupByLibrary.simpleMessage("صفحہ"),
        "pleaseCheckConnection_openai": MessageLookupByLibrary.simpleMessage(
            "براہ کرم اپنا کنکشن چیک کریں اور دوبارہ کوشش کریں!"),
        "pleaseInputFillAllFields_openai": MessageLookupByLibrary.simpleMessage(
            "براہ کرم تمام فیلڈز کو پُر کریں۔"),
        "pleaseInputKey_openai":
            MessageLookupByLibrary.simpleMessage("براہ کرم کلید داخل کریں۔"),
        "prompt_openai": MessageLookupByLibrary.simpleMessage("فوری طور پر"),
        "putKeyHere_openai":
            MessageLookupByLibrary.simpleMessage("اپنی چابی یہاں رکھو"),
        "regenerateResponse_openai":
            MessageLookupByLibrary.simpleMessage("ردعمل کو دوبارہ تخلیق کریں۔"),
        "remaining_openai": MessageLookupByLibrary.simpleMessage("باقی ہے"),
        "removeKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("کلید کو ہٹانا ناکام ہو گیا۔"),
        "removeKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
            "کلید کو کامیابی سے ہٹا دیا گیا۔"),
        "remove_openai": MessageLookupByLibrary.simpleMessage("دور"),
        "resetSettings_openai": MessageLookupByLibrary.simpleMessage(
            "ترتیبات کو دوبارہ ترتیب دیں۔"),
        "reset_openai": MessageLookupByLibrary.simpleMessage("ری سیٹ کریں"),
        "saveKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
            "کلید محفوظ کرنا ناکام ہو گیا۔"),
        "saveKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
            "کلید کو کامیابی سے محفوظ کر لیا گیا۔"),
        "saveKey_openai":
            MessageLookupByLibrary.simpleMessage("کلید محفوظ کریں۔"),
        "save_openai": MessageLookupByLibrary.simpleMessage("محفوظ کریں"),
        "searchByPrompt_openai":
            MessageLookupByLibrary.simpleMessage("پرامپٹ سے تلاش کریں..."),
        "sectionKeywords_openai":
            MessageLookupByLibrary.simpleMessage("سیکشن مطلوبہ الفاظ"),
        "sectionTopic_openai":
            MessageLookupByLibrary.simpleMessage("سیکشن کا موضوع"),
        "selectChatFailed_openai":
            MessageLookupByLibrary.simpleMessage("چیٹ ناکام کو منتخب کریں۔"),
        "selectPrompt_openai":
            MessageLookupByLibrary.simpleMessage("پرامپٹ کو منتخب کریں۔"),
        "settings_openai": MessageLookupByLibrary.simpleMessage("ترتیبات۔"),
        "share_openai": MessageLookupByLibrary.simpleMessage("بانٹیں"),
        "skills_openai": MessageLookupByLibrary.simpleMessage("ہنر"),
        "somethingWentWrong_openai":
            MessageLookupByLibrary.simpleMessage("کچھ غلط ہو گیا!!!"),
        "somethingWhenWrong_openai": MessageLookupByLibrary.simpleMessage(
            "کچھ غلط ہو گیا! براہ کرم کچھ دیر بعد کوشش کریں. بہت بہت شکریہ!"),
        "speechNotAvailable_openai":
            MessageLookupByLibrary.simpleMessage("تقریر دستیاب نہیں ہے۔"),
        "style_openai": MessageLookupByLibrary.simpleMessage("انداز"),
        "subscriptionExpiredDate_openai": m2,
        "tapTheMicToTalk_openai": MessageLookupByLibrary.simpleMessage(
            "بات کرنے کے لیے مائیک کو تھپتھپائیں۔"),
        "textGenerate_openai":
            MessageLookupByLibrary.simpleMessage("متن تیار کریں۔"),
        "textGenerator_openai":
            MessageLookupByLibrary.simpleMessage("ٹیکسٹ جنریٹر"),
        "timeGenerate_openai": m3,
        "typeAMessage_openai":
            MessageLookupByLibrary.simpleMessage("ایک پیغام ٹائپ کریں..."),
        "viewType_openai": MessageLookupByLibrary.simpleMessage("قسم دیکھیں"),
        "view_openai": MessageLookupByLibrary.simpleMessage("دیکھیں"),
        "write_openai": MessageLookupByLibrary.simpleMessage("لکھیں۔")
      };
}
