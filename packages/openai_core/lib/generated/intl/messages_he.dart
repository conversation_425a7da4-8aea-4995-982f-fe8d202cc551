// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a he locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'he';

  static String m0(limit) => "יש רק ${limit}חיפוש תמונות בגרסה החינמית.";

  static String m1(limit) => "ניתן להציג עד ${limit} הודעות רק בגרסה החינמית.";

  static String m2(date) => "תאריך תפוגה של המנוי ${date}";

  static String m3(number) =>
      "צור (${number} ${Intl.plural(number, one: 'time', other: 'times')})";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "about_openai": MessageLookupByLibrary.simpleMessage("על אודות"),
    "apply_openai": MessageLookupByLibrary.simpleMessage("להגיש מועמדות"),
    "artist_openai": MessageLookupByLibrary.simpleMessage("אמן"),
    "cancel_openai": MessageLookupByLibrary.simpleMessage("בטל"),
    "chatDetail_openai": MessageLookupByLibrary.simpleMessage("פרטי צ\'אט"),
    "chatGPT_openai": MessageLookupByLibrary.simpleMessage("צ\'אט GPT"),
    "chatWithBot_openai": MessageLookupByLibrary.simpleMessage("צ\'אט עם Bot"),
    "chat_openai": MessageLookupByLibrary.simpleMessage("צ\'אט"),
    "chooseArtist_openai": MessageLookupByLibrary.simpleMessage(
      "בחר אמן לתמונה שלך",
    ),
    "chooseDetail_openai": MessageLookupByLibrary.simpleMessage(
      "בחר פרט עבור התמונה שלך",
    ),
    "chooseMedium_openai": MessageLookupByLibrary.simpleMessage(
      "בחר מדיום עבור התמונה שלך",
    ),
    "chooseMood_openai": MessageLookupByLibrary.simpleMessage(
      "בחר מצב רוח לתמונה שלך",
    ),
    "chooseUseCase_openai": MessageLookupByLibrary.simpleMessage(
      "בחר מקרה שימוש",
    ),
    "choseStyle_openai": MessageLookupByLibrary.simpleMessage(
      "בחר סגנון לתמונה שלך",
    ),
    "clearConfirm_openai": MessageLookupByLibrary.simpleMessage(
      "האם אתה בטוח לנקות תוכן?",
    ),
    "clearContent_openai": MessageLookupByLibrary.simpleMessage("תוכן ברור"),
    "clearConversation_openai": MessageLookupByLibrary.simpleMessage(
      "שיחה ברורה",
    ),
    "clear_openai": MessageLookupByLibrary.simpleMessage("ברור"),
    "confirmDeleteItem_openai": MessageLookupByLibrary.simpleMessage(
      "האם אתה בטוח שברצונך למחוק פריט זה?",
    ),
    "confirmDelete_openai": MessageLookupByLibrary.simpleMessage(
      "אנא אשר אם ברצונך להמשיך במחיקת פריט זה. לא ניתן לבטל את הפעולה הזו.",
    ),
    "confirmRemoveKey_openai": MessageLookupByLibrary.simpleMessage(
      "האם אתה בטוח להסיר את המפתח?",
    ),
    "confirm_openai": MessageLookupByLibrary.simpleMessage("אשר"),
    "copiedToClipboard_openai": MessageLookupByLibrary.simpleMessage(
      "הועתק תוכן ללוח",
    ),
    "copy_openai": MessageLookupByLibrary.simpleMessage("עותק"),
    "createChatFailed_openai": MessageLookupByLibrary.simpleMessage(
      "יצירת צ\'אט נכשלה",
    ),
    "deleteFailed_openai": MessageLookupByLibrary.simpleMessage("המחיקה נכשלה"),
    "delete_openai": MessageLookupByLibrary.simpleMessage("מחק"),
    "detail_openai": MessageLookupByLibrary.simpleMessage("פרט"),
    "download_openai": MessageLookupByLibrary.simpleMessage("הורד"),
    "edit_openai": MessageLookupByLibrary.simpleMessage("ערוך"),
    "failedToGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "היצירה נכשלה",
    ),
    "generate_openai": MessageLookupByLibrary.simpleMessage("ליצור"),
    "grid_openai": MessageLookupByLibrary.simpleMessage("רשת"),
    "imageGenerate_openai": MessageLookupByLibrary.simpleMessage("ליצור תמונה"),
    "imageSize_openai": MessageLookupByLibrary.simpleMessage("גודל תמונה"),
    "inputKey_openai": MessageLookupByLibrary.simpleMessage("מפתח קלט"),
    "interest_openai": MessageLookupByLibrary.simpleMessage("ריבית"),
    "introAboutKey_openai": MessageLookupByLibrary.simpleMessage(
      "מפתח ה-API שלך מאוחסן באופן מקומי בנייד שלך ולעולם לא נשלח לשום מקום אחר. אתה יכול לשמור את המפתח שלך כדי להשתמש בו מאוחר יותר. אתה יכול גם להסיר את המפתח שלך אם אתה לא רוצה להשתמש בו יותר.",
    ),
    "invalidKey_openai": MessageLookupByLibrary.simpleMessage("מפתח לא חוקי"),
    "jobRole_openai": MessageLookupByLibrary.simpleMessage("תיאור המשרה"),
    "jobSkills_openai": MessageLookupByLibrary.simpleMessage("כישורי עבודה"),
    "layoutStyle_openai": MessageLookupByLibrary.simpleMessage("סגנון פריסה"),
    "limitImage_openai": m0,
    "limitTheText_openai": m1,
    "listening_openai": MessageLookupByLibrary.simpleMessage("הַקשָׁבָה..."),
    "loadKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "מפתח הטעינה נכשל",
    ),
    "loadKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "טעינת מפתח הצלחה",
    ),
    "manage_openai": MessageLookupByLibrary.simpleMessage("לנהל"),
    "medium_openai": MessageLookupByLibrary.simpleMessage("בינוני"),
    "mood_openai": MessageLookupByLibrary.simpleMessage("מַצַב רוּחַ"),
    "moreOptions_openai": MessageLookupByLibrary.simpleMessage(
      "אפשרויות נוספות",
    ),
    "newChat_openai": MessageLookupByLibrary.simpleMessage("שיחה חדשה"),
    "noImageGenerate_openai": MessageLookupByLibrary.simpleMessage(
      "אין ליצור תמונה",
    ),
    "numberOfImagesCondition_openai": MessageLookupByLibrary.simpleMessage(
      "מספר התמונות שיש ליצור. חייב להיות בין 1 ל-10.",
    ),
    "numberOfImages_openai": MessageLookupByLibrary.simpleMessage(
      "מספר תמונות",
    ),
    "options_openai": MessageLookupByLibrary.simpleMessage("אפשרויות"),
    "page_openai": MessageLookupByLibrary.simpleMessage("עמוד"),
    "pleaseCheckConnection_openai": MessageLookupByLibrary.simpleMessage(
      "בבקשה תבדוק את החיבור שלך ונסה שוב!",
    ),
    "pleaseInputFillAllFields_openai": MessageLookupByLibrary.simpleMessage(
      "בבקשה מלא את כל השדות",
    ),
    "pleaseInputKey_openai": MessageLookupByLibrary.simpleMessage(
      "נא להזין מפתח",
    ),
    "prompt_openai": MessageLookupByLibrary.simpleMessage("מיידי"),
    "putKeyHere_openai": MessageLookupByLibrary.simpleMessage(
      "שים את המפתח שלך כאן",
    ),
    "regenerateResponse_openai": MessageLookupByLibrary.simpleMessage(
      "חידוש תגובה",
    ),
    "remaining_openai": MessageLookupByLibrary.simpleMessage("הנותרים"),
    "removeKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "הסרת מפתח נכשלה",
    ),
    "removeKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "מפתח הוסר בהצלחה",
    ),
    "remove_openai": MessageLookupByLibrary.simpleMessage("הסר"),
    "resetSettings_openai": MessageLookupByLibrary.simpleMessage("אפס הגדרות"),
    "reset_openai": MessageLookupByLibrary.simpleMessage("אפס"),
    "saveKeyFailed_openai": MessageLookupByLibrary.simpleMessage(
      "מפתח שמירה נכשל",
    ),
    "saveKeySuccess_openai": MessageLookupByLibrary.simpleMessage(
      "מפתח נשמר בהצלחה",
    ),
    "saveKey_openai": MessageLookupByLibrary.simpleMessage("מפתח שמור"),
    "save_openai": MessageLookupByLibrary.simpleMessage("להציל"),
    "searchByPrompt_openai": MessageLookupByLibrary.simpleMessage(
      "חפש לפי הנחיה...",
    ),
    "sectionKeywords_openai": MessageLookupByLibrary.simpleMessage(
      "מדור מילות מפתח",
    ),
    "sectionTopic_openai": MessageLookupByLibrary.simpleMessage("נושא המדור"),
    "selectChatFailed_openai": MessageLookupByLibrary.simpleMessage(
      "בחר צ\'אט נכשל",
    ),
    "selectPrompt_openai": MessageLookupByLibrary.simpleMessage("בחר הנחה"),
    "settings_openai": MessageLookupByLibrary.simpleMessage("הגדרות"),
    "share_openai": MessageLookupByLibrary.simpleMessage("שתף"),
    "skills_openai": MessageLookupByLibrary.simpleMessage("מיומנויות"),
    "somethingWentWrong_openai": MessageLookupByLibrary.simpleMessage(
      "משהו השתבש!!!",
    ),
    "somethingWhenWrong_openai": MessageLookupByLibrary.simpleMessage(
      "משהו השתבש! בבקשה נסה שוב מאוחר יותר. תודה רבה לך!",
    ),
    "speechNotAvailable_openai": MessageLookupByLibrary.simpleMessage(
      "דיבור אינו זמין",
    ),
    "style_openai": MessageLookupByLibrary.simpleMessage("סגנון"),
    "subscriptionExpiredDate_openai": m2,
    "tapTheMicToTalk_openai": MessageLookupByLibrary.simpleMessage(
      "הקש על המיקרופון כדי לדבר",
    ),
    "textGenerate_openai": MessageLookupByLibrary.simpleMessage("יצירת טקסט"),
    "textGenerator_openai": MessageLookupByLibrary.simpleMessage("מחולל טקסט"),
    "timeGenerate_openai": m3,
    "typeAMessage_openai": MessageLookupByLibrary.simpleMessage(
      "הקלד הודעה...",
    ),
    "viewType_openai": MessageLookupByLibrary.simpleMessage("סוג תצוגה"),
    "view_openai": MessageLookupByLibrary.simpleMessage("נוף"),
    "write_openai": MessageLookupByLibrary.simpleMessage("לִכתוֹב"),
  };
}
