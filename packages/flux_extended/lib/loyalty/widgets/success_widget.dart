import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flux_localization/flux_localization.dart';
import 'package:fstore/common/constants.dart';

class SuccessWidget extends StatelessWidget {
  const SuccessWidget({super.key, required this.onScan, this.isAdd = true});
  final VoidCallback onScan;
  final bool isAdd;

  @override
  Widget build(BuildContext context) {
    var title = isAdd
        ? S.of(context).pointsAddedSuccessfully
        : S.of(context).pointsRedeemedSuccessfully;
    var subtitle =
        isAdd ? S.of(context).pointsAddedMsg : S.of(context).pointsRedeemedMsg;
    return Stack(
      children: [
        SizedBox(
          width: double.infinity,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.max,
            children: [
              const SizedBox(height: 50),
              SvgPicture.asset(
                'assets/icons/loyalty/ic_success.svg',
                width: 80.0,
                height: 80.0,
              ),
              const SizedBox(height: 50),
              Text(
                title,
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(fontWeight: FontWeight.w600, fontSize: 18.0),
              ),
              const SizedBox(height: 10),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: kGrey400,
                    fontWeight: FontWeight.w400,
                    fontSize: 14.0),
              ),
            ],
          ),
        ),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            padding: const EdgeInsets.only(bottom: 15.0, top: 15.0),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.5),
                  spreadRadius: 5,
                  blurRadius: 7,
                  offset: const Offset(0, 3), // changes position of shadow
                ),
              ],
            ),
            child: SafeArea(
              child: Row(
                children: [
                  const SizedBox(width: 16.0),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        height: 44,
                        alignment: FractionalOffset.center,
                        decoration: BoxDecoration(
                            borderRadius:
                                const BorderRadius.all(Radius.circular(22.0)),
                            border: Border.all(color: kGrey400)),
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Text(
                          S.of(context).back,
                          style: Theme.of(context)
                              .textTheme
                              .titleSmall
                              ?.copyWith(
                                  fontWeight: FontWeight.w600, fontSize: 14.0),
                          softWrap: true,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16.0),
                  Expanded(
                    child: GestureDetector(
                      onTap: onScan,
                      child: Container(
                        height: 44,
                        alignment: FractionalOffset.center,
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          borderRadius:
                              const BorderRadius.all(Radius.circular(22.0)),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Text(
                          S.of(context).scanQRCode,
                          style: Theme.of(context)
                              .textTheme
                              .titleSmall
                              ?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14.0),
                          softWrap: true,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16.0),
                ],
              ),
            ),
          ),
        )
      ],
    );
  }
}
