// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a id locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'id';

  static String m0(limit) =>
      "Hanya ada ${limit}x penelusuran gambar dalam versi gratis.";

  static String m1(limit) =>
      "Hingga ${limit} pesan hanya dapat ditampilkan dalam versi gratis.";

  static String m2(date) => "Tanggal kedaluwarsa langganan ${date}";

  static String m3(number) =>
      "Hasilkan (${number} ${Intl.plural(number, one: 'time', other: 'times')})";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "about_openai": MessageLookupByLibrary.simpleMessage("Tentang"),
        "apply_openai": MessageLookupByLibrary.simpleMessage("Menerapkan"),
        "artist_openai": MessageLookupByLibrary.simpleMessage("Artis"),
        "cancel_openai": MessageLookupByLibrary.simpleMessage("Membatalkan"),
        "chatDetail_openai":
            MessageLookupByLibrary.simpleMessage("Detail Obrolan"),
        "chatGPT_openai": MessageLookupByLibrary.simpleMessage("Obrolan GPT"),
        "chatWithBot_openai":
            MessageLookupByLibrary.simpleMessage("Mengobrol dengan Bot"),
        "chat_openai": MessageLookupByLibrary.simpleMessage("Obrolan"),
        "chooseArtist_openai": MessageLookupByLibrary.simpleMessage(
            "Pilih artis untuk gambar Anda"),
        "chooseDetail_openai": MessageLookupByLibrary.simpleMessage(
            "Pilih detail untuk gambar Anda"),
        "chooseMedium_openai": MessageLookupByLibrary.simpleMessage(
            "Pilih media untuk gambar Anda"),
        "chooseMood_openai": MessageLookupByLibrary.simpleMessage(
            "Pilih mood untuk gambar Anda"),
        "chooseUseCase_openai":
            MessageLookupByLibrary.simpleMessage("Pilih kasus penggunaan"),
        "choseStyle_openai": MessageLookupByLibrary.simpleMessage(
            "Pilih gaya untuk gambar Anda"),
        "clearConfirm_openai": MessageLookupByLibrary.simpleMessage(
            "Apakah Anda yakin ingin menghapus konten?"),
        "clearContent_openai":
            MessageLookupByLibrary.simpleMessage("Hapus konten"),
        "clearConversation_openai":
            MessageLookupByLibrary.simpleMessage("Percakapan yang jelas"),
        "clear_openai": MessageLookupByLibrary.simpleMessage("Bersih"),
        "confirmDeleteItem_openai": MessageLookupByLibrary.simpleMessage(
            "Apakah Anda yakin ingin menghapus item ini?"),
        "confirmDelete_openai": MessageLookupByLibrary.simpleMessage(
            "Harap konfirmasi jika Anda ingin melanjutkan penghapusan item ini. Anda tidak dapat membatalkan tindakan ini."),
        "confirmRemoveKey_openai": MessageLookupByLibrary.simpleMessage(
            "Apakah Anda yakin untuk menghapus kunci?"),
        "confirm_openai": MessageLookupByLibrary.simpleMessage("Konfirmasi"),
        "copiedToClipboard_openai": MessageLookupByLibrary.simpleMessage(
            "Menyalin konten ke clipboard"),
        "copy_openai": MessageLookupByLibrary.simpleMessage("Salinan"),
        "createChatFailed_openai":
            MessageLookupByLibrary.simpleMessage("Buat Obrolan Gagal"),
        "deleteFailed_openai":
            MessageLookupByLibrary.simpleMessage("Hapus Gagal"),
        "delete_openai": MessageLookupByLibrary.simpleMessage("Menghapus"),
        "detail_openai": MessageLookupByLibrary.simpleMessage("Detail"),
        "download_openai": MessageLookupByLibrary.simpleMessage("Unduh"),
        "edit_openai": MessageLookupByLibrary.simpleMessage("Edit"),
        "failedToGenerate_openai":
            MessageLookupByLibrary.simpleMessage("Gagal menghasilkan"),
        "generate_openai": MessageLookupByLibrary.simpleMessage("Menghasilkan"),
        "grid_openai": MessageLookupByLibrary.simpleMessage("Kisi"),
        "imageGenerate_openai":
            MessageLookupByLibrary.simpleMessage("Hasilkan gambar"),
        "imageSize_openai":
            MessageLookupByLibrary.simpleMessage("Ukuran gambar"),
        "inputKey_openai":
            MessageLookupByLibrary.simpleMessage("Kunci Masukan"),
        "interest_openai": MessageLookupByLibrary.simpleMessage("Minat"),
        "introAboutKey_openai": MessageLookupByLibrary.simpleMessage(
            "Kunci API Anda disimpan secara lokal di ponsel Anda dan tidak pernah dikirim ke tempat lain. Anda dapat menyimpan kunci untuk digunakan nanti. Anda juga dapat menghapus kunci jika tidak ingin menggunakannya lagi."),
        "invalidKey_openai":
            MessageLookupByLibrary.simpleMessage("Kunci tidak sesuai"),
        "jobRole_openai":
            MessageLookupByLibrary.simpleMessage("Peran Pekerjaan"),
        "jobSkills_openai":
            MessageLookupByLibrary.simpleMessage("Keterampilan Pekerjaan"),
        "layoutStyle_openai":
            MessageLookupByLibrary.simpleMessage("gaya latar"),
        "limitImage_openai": m0,
        "limitTheText_openai": m1,
        "listening_openai":
            MessageLookupByLibrary.simpleMessage("Mendengarkan..."),
        "loadKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("Memuat Kunci Gagal"),
        "loadKeySuccess_openai":
            MessageLookupByLibrary.simpleMessage("Muat Kunci Sukses"),
        "manage_openai": MessageLookupByLibrary.simpleMessage("Mengelola"),
        "medium_openai": MessageLookupByLibrary.simpleMessage("Medium"),
        "mood_openai": MessageLookupByLibrary.simpleMessage("Suasana hati"),
        "moreOptions_openai":
            MessageLookupByLibrary.simpleMessage("Lebih banyak pilihan"),
        "newChat_openai": MessageLookupByLibrary.simpleMessage("Obrolan Baru"),
        "noImageGenerate_openai": MessageLookupByLibrary.simpleMessage(
            "Tidak ada gambar yang dihasilkan"),
        "numberOfImagesCondition_openai": MessageLookupByLibrary.simpleMessage(
            "Jumlah gambar yang akan dihasilkan. Harus antara 1 dan 10."),
        "numberOfImages_openai":
            MessageLookupByLibrary.simpleMessage("Jumlah gambar"),
        "options_openai": MessageLookupByLibrary.simpleMessage("Pilihan"),
        "page_openai": MessageLookupByLibrary.simpleMessage("Halaman"),
        "pleaseCheckConnection_openai": MessageLookupByLibrary.simpleMessage(
            "Periksa koneksi Anda dan coba lagi!"),
        "pleaseInputFillAllFields_openai":
            MessageLookupByLibrary.simpleMessage("Harap isi semua bidang"),
        "pleaseInputKey_openai":
            MessageLookupByLibrary.simpleMessage("Harap masukkan kunci"),
        "prompt_openai": MessageLookupByLibrary.simpleMessage("Cepat"),
        "putKeyHere_openai":
            MessageLookupByLibrary.simpleMessage("Letakkan kunci Anda di sini"),
        "regenerateResponse_openai":
            MessageLookupByLibrary.simpleMessage("Regenerasi respons"),
        "remaining_openai": MessageLookupByLibrary.simpleMessage("tersisa"),
        "removeKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("Hapus Kunci Gagal"),
        "removeKeySuccess_openai":
            MessageLookupByLibrary.simpleMessage("Kunci Dihapus Berhasil"),
        "remove_openai": MessageLookupByLibrary.simpleMessage("Menghapus"),
        "resetSettings_openai":
            MessageLookupByLibrary.simpleMessage("Atur Ulang Pengaturan"),
        "reset_openai": MessageLookupByLibrary.simpleMessage("Setel ulang"),
        "saveKeyFailed_openai":
            MessageLookupByLibrary.simpleMessage("Simpan Kunci Gagal"),
        "saveKeySuccess_openai":
            MessageLookupByLibrary.simpleMessage("Kunci Tersimpan Berhasil"),
        "saveKey_openai": MessageLookupByLibrary.simpleMessage("Simpan Kunci"),
        "save_openai": MessageLookupByLibrary.simpleMessage("Menyimpan"),
        "searchByPrompt_openai": MessageLookupByLibrary.simpleMessage(
            "Telusuri berdasarkan Prompt..."),
        "sectionKeywords_openai":
            MessageLookupByLibrary.simpleMessage("Kata Kunci Bagian"),
        "sectionTopic_openai":
            MessageLookupByLibrary.simpleMessage("Topik Bagian"),
        "selectChatFailed_openai":
            MessageLookupByLibrary.simpleMessage("Pilih Obrolan Gagal"),
        "selectPrompt_openai":
            MessageLookupByLibrary.simpleMessage("Pilih Prompt"),
        "settings_openai": MessageLookupByLibrary.simpleMessage("Pengaturan"),
        "share_openai": MessageLookupByLibrary.simpleMessage("Bagikan"),
        "skills_openai": MessageLookupByLibrary.simpleMessage("Keterampilan"),
        "somethingWentWrong_openai":
            MessageLookupByLibrary.simpleMessage("Ada yang salah!!!"),
        "somethingWhenWrong_openai": MessageLookupByLibrary.simpleMessage(
            "Ada yang salah! Coba lagi nanti. Terima kasih banyak!"),
        "speechNotAvailable_openai":
            MessageLookupByLibrary.simpleMessage("Ucapan tidak tersedia"),
        "style_openai": MessageLookupByLibrary.simpleMessage("Gaya"),
        "subscriptionExpiredDate_openai": m2,
        "tapTheMicToTalk_openai": MessageLookupByLibrary.simpleMessage(
            "Ketuk mikrofon untuk berbicara"),
        "textGenerate_openai":
            MessageLookupByLibrary.simpleMessage("Menghasilkan teks"),
        "textGenerator_openai":
            MessageLookupByLibrary.simpleMessage("Pembuat Teks"),
        "timeGenerate_openai": m3,
        "typeAMessage_openai":
            MessageLookupByLibrary.simpleMessage("Ketik pesan..."),
        "viewType_openai":
            MessageLookupByLibrary.simpleMessage("Jenis tampilan"),
        "view_openai": MessageLookupByLibrary.simpleMessage("Melihat"),
        "write_openai": MessageLookupByLibrary.simpleMessage("menulis")
      };
}
