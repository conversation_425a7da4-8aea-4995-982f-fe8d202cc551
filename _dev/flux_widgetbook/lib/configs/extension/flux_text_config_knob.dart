import 'package:flutter/material.dart';
import 'package:flux_ui/flux_ui.dart';
import 'package:widgetbook/widgetbook.dart';

extension FluxTextConfigKnobsExtension on KnobsBuilder {
  FluxTextConfig fluxTextConfig({
    String prefixLabel = 'Label',
    String initText = 'initValue',
    int? initMaxLines,
    TextStyleSource initTextStyle = TextStyleSource.bodyMedium,
    String initFontFamily = 'Roboto',
    FontWeight initFontWeight = FontWeight.w400,
    double initFontSize = 14.0,
    Color? initColor,
  }) =>
      FluxTextConfig(
        text: string(label: '$prefixLabel Text', initialValue: initText),
        styleConfig: FluxTextStyleConfig(
          maxLines: _processMaxLines(intOrNull.slider(
            label: '$prefixLabel Max Lines',
            min: 0,
            max: 10,
            initialValue: initMaxLines,
          )),
          styleSource: list(
            label: '$prefixLabel Text Style',
            options: TextStyleSource.values,
            initialOption: initTextStyle,
          ),
          customStyle: AppTextStyle(
            fontFamily: string(
              label: '$prefixLabel Font Family',
              initialValue: initFontFamily,
            ),
            fontWeight: list(
              label: '$prefixLabel Font Weight',
              options: FontWeight.values,
              initialOption: initFontWeight,
            ),
            fontSize: this.double.slider(
                  label: '$prefixLabel Font Size',
                  min: 8,
                  max: 50,
                  initialValue: initFontSize,
                ),
            color: AppColor.solid(colorOrNull(
              label: '$prefixLabel Color',
              initialValue: initColor,
            )),
          ),
        ),
      );

  int? _processMaxLines(int? slider) {
    if (slider == null) {
      return null;
    }
    if (slider == 0) {
      return null;
    }
    return slider;
  }

  FluxTextStyleConfig fluxTextStyleConfig({
    String label = '',
    String initFontFamily = 'Roboto',
    FontWeight initFontWeight = FontWeight.w400,
    double initFontSize = 14.0,
    Color? initColor,
    TextStyleSource initTextStyle = TextStyleSource.bodyMedium,
  }) =>
      FluxTextStyleConfig(
        maxLines: _processMaxLines(intOrNull.slider(
          label: '$label Max Lines',
          min: 0,
          max: 10,
          initialValue: 1,
        )),
        minLines: _processMaxLines(intOrNull.slider(
          label: '$label Min Lines',
          min: 0,
          max: 10,
          initialValue: 1,
        )),
        styleSource: list(
          label: '$label Text Style',
          options: TextStyleSource.values,
          initialOption: initTextStyle,
        ),
        customStyle: AppTextStyle(
          fontFamily: string(
            label: '$label Font Family',
            initialValue: initFontFamily,
          ),
          fontWeight: list(
            label: '$label Font Weight',
            options: FontWeight.values,
            initialOption: initFontWeight,
          ),
          fontSize: this.double.slider(
                label: '$label Font Size',
                min: 0,
                max: 50,
                initialValue: initFontSize,
              ),
          color: AppColor.solid(colorOrNull(
            label: '$label Color',
            initialValue: initColor,
          )),
        ),
      );
}
