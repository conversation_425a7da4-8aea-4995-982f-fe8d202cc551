import 'package:flux_ui/flux_ui.dart';
import 'package:widgetbook/widgetbook.dart';

import 'edge_insets_knob.dart';

extension FluxListViewConfigKnob on KnobsBuilder {
  FluxListViewConfig fluxListViewConfig({
    String label = '',
    double itemSize = 1.5,
    ItemSizeAdvanceType itemSizeType = ItemSizeAdvanceType.gridColumns,
    double itemSpacing = 8.0,
    double paddingHorizontal = 16.0,
    double paddingVertical = 16.0,
  }) =>
      FluxListViewConfig(
        itemSize: ItemSizeAdvanceConfig(
          value: 1.5,
          type: list(
            label: '$label List Item Size',
            options: ItemSizeAdvanceType.values,
            initialOption: itemSizeType,
          ),
        ),
        padding: edgeInsetsSymmetric(
          label: '$label List',
          vertical: paddingVertical,
          horizontal: paddingHorizontal,
        ),
        itemSpacing: this.double.slider(
              label: '$label List Item Spacing',
              min: 0,
              max: 48,
              initialValue: itemSpacing,
            ),
      );
}
