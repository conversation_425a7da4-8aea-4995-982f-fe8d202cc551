<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools">
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK"/>
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS"/>
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="com.android.vending.BILLING"/>
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM"
                     android:maxSdkVersion="32"/>
    <uses-permission android:name="android.permission.USE_EXACT_ALARM"/>
    <!-- io.flutter.app.FlutterApplication is an android.app.Application that
         calls FlutterMain.startInitialization(this); in its onCreate method.
         In most cases you can leave this as-is, but you if you want to provide
         additional functionality it is fine to subclass or reimplement
         FlutterApplication and put your custom class here. -->

    <queries>
        <intent>
            <action android:name="android.intent.action.SENDTO"/>
            <data android:scheme="mailto"/>
        </intent>
    </queries>
    <application
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name"
            android:name="${applicationName}"
            android:roundIcon="@mipmap/ic_launcher_round"
            android:usesCleartextTraffic="true">
        <activity
                android:name=".MainActivity"
                android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
                android:exported="true"
                tools:ignore="Instantiatable"
                android:hardwareAccelerated="true"
                android:launchMode="singleTop"
                android:theme="@style/LaunchTheme"
                android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android themes to apply to this Activity as soon as
                 the Android process has started. This themes is visible to the user
                 while the Flutter UI initializes. After that, this themes continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                    android:name="io.flutter.embedding.android.NormalTheme"
                    android:resource="@style/NormalTheme"/>
            <!-- Displays an Android View that continues showing the launch screen
                 Drawable until Flutter paints its first frame, then this splash
                 screen fades out. A splash screen is useful to avoid any visual
                 gap between the end of Android's launch screen and the painting of
                 Flutter's first frame. -->
            <meta-data
                    android:name="io.flutter.embedding.android.SplashScreenDrawable"
                    android:resource="@drawable/launch_background"/>
            <!-- This keeps the window background of the activity showing
                 until Flutter renders its first frame. It can be removed if
                 there is no splash screen (such as the default splash screen
                 defined in @style/LaunchTheme). -->

            <!-- If you use a third-party plugin to handle deep links, such as app_links, Flutter's default deeplink handler will break these plugins.
                To opt out of using Flutter's default deep link handler, add the following metadata tag to <activity>:
                Ref: https://docs.flutter.dev/cookbook/navigation/set-up-app-links#2-modify-androidmanifest-xml -->
            <meta-data android:name="flutter_deeplinking_enabled" android:value="false"/>
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
            <intent-filter>
                <action android:name="FLUTTER_NOTIFICATION_CLICK"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>

        <meta-data
                android:name="flutterEmbedding"
                android:value="2"/>


        <meta-data
                android:name="com.google.firebase.messaging.default_notification_channel_id"
                android:value="high_importance_channel"/>
        <meta-data
                android:name="Medita Audio Playback"
                android:value="com.inapps.medita.audio"/>
        <meta-data
                android:name="com.facebook.sdk.ApplicationId"
                android:value="@string/facebook_app_id"/>
        <meta-data android:name="com.facebook.sdk.ClientToken"
                   android:value="@string/facebook_client_token"/>

        <activity
                android:name="com.facebook.FacebookActivity"
                android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
                android:label="@string/app_name"/>

        <activity
                android:name="com.facebook.CustomTabActivity"
                android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>

                <data android:scheme="@string/fb_login_protocol_scheme"/>
            </intent-filter>
        </activity>

        <service
                android:name="io.flutter.plugins.androidalarmmanager.AlarmService"
                tools:ignore="Instantiatable"
                android:exported="true"
                android:permission="android.permission.BIND_JOB_SERVICE"/>

        <receiver
                android:name="io.flutter.plugins.androidalarmmanager.AlarmBroadcastReceiver"
                tools:ignore="Instantiatable"
                android:exported="true"/>
        <receiver
                android:name="io.flutter.plugins.androidalarmmanager.RebootBroadcastReceiver"
                android:enabled="false"
                tools:ignore="Instantiatable"
                android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
            </intent-filter>
        </receiver>


        <receiver
                tools:ignore="Instantiatable"
                android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
                android:exported="false">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"></action>
            </intent-filter>
        </receiver>
        <receiver
                tools:ignore="Instantiatable"
                android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver"
                android:exported="false"/>

        <service
                android:name="com.dexterous.flutterlocalnotifications.ForegroundService"
                android:exported="false"
                android:stopWithTask="false"/>

        <activity
                tools:ignore="Instantiatable"
                android:name="com.yalantis.ucrop.UCropActivity"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.AppCompat.Light.NoActionBar"/>

        <activity
                android:name="com.braintreepayments.api.BraintreeBrowserSwitchActivity"
                tools:ignore="Instantiatable"
                android:exported="false"
                android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>

                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>

                <data android:scheme="${applicationId}.braintree"/>
            </intent-filter>
            <meta-data
                    android:name="com.google.android.gms.wallet.api.enabled"
                    android:value="true"/>
            <!-- <meta-data android:name="flutterEmbedding" android:value="2" /> -->
        </activity>

        <!-- ADD THIS "SERVICE" element -->
        <service android:name="com.ryanheise.audioservice.AudioService"
                 android:foregroundServiceType="mediaPlayback"
                 android:exported="true" tools:ignore="Instantiatable">
            <intent-filter>
                <action android:name="android.media.browse.MediaBrowserService"/>
            </intent-filter>
        </service>

        <!-- ADD THIS "RECEIVER" element -->
        <receiver android:name="com.ryanheise.audioservice.MediaButtonReceiver"
                  android:exported="true" tools:ignore="Instantiatable">
            <intent-filter>
                <action android:name="android.intent.action.MEDIA_BUTTON"/>
            </intent-filter>
        </receiver>
    </application>
</manifest>